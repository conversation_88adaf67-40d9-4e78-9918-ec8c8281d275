package logic

import (
	"context"
	"fmt"

	"zblockchain/app/binance/config"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"
	binanceLogic "zblockchain/app/binance/logic"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/logx"
)

type MartingaleBacktestLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMartingaleBacktestLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MartingaleBacktestLogic {
	return &MartingaleBacktestLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MartingaleBacktestLogic) MartingaleBacktest(req *types.MartingaleBacktestRequest) (resp *types.MartingaleBacktestResponse, err error) {
	// 配置币安 API
	client := futures.NewClient(config.GlobalConfig.ApiKey, config.GlobalConfig.ApiSecret)

	// 设置回测参数
	leverage := 10.0
	initialBalance := 10000.0
	feeRate := 0.04 / 100
	stopLossMultiplier := 1.5
	takeProfitMultiplier := 2.0
	maintenanceMarginRate := 0.005

	// 获取历史 K 线数据
	klines, err := binanceLogic.GetHistoricalKlines(client, req.Symbol, req.Interval, req.StartTime, req.EndTime)
	if err != nil {
		return &types.MartingaleBacktestResponse{
			Success: false,
			Message: fmt.Sprintf("获取 K 线数据失败: %v", err),
		}, nil
	}

	// 获取历史资金费率
	fundingRates, err := binanceLogic.GetHistoricalFundingRates(client, req.Symbol, req.StartTime, req.EndTime)
	if err != nil {
		return &types.MartingaleBacktestResponse{
			Success: false,
			Message: fmt.Sprintf("获取资金费率失败: %v", err),
		}, nil
	}

	var bestParams binanceLogic.MartingaleParams

	if req.UseOptimize {
		// 运行完整参数优化（5个策略组合，并发执行）
		bestParams, _ = binanceLogic.OptimizeMartingaleParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	} else if req.QuickOptimize {
		// 运行快速优化（3个策略组合，顺序执行）
		bestParams, _ = binanceLogic.QuickOptimizeMartingaleParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	} else {
		// 使用默认参数（无优化，最快）
		bestParams = binanceLogic.GetDefaultMartingaleParams(initialBalance)
	}

	// 使用最佳参数运行回测
	bt := binanceLogic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt.Run(klines, fundingRates, func(klines []*binanceLogic.Kline, bt *binanceLogic.Backtest, index int) (binanceLogic.TradeSignal, float64) {
		return binanceLogic.MartingaleStrategy(klines, bt, index, bestParams)
	})

	// 转换马丁层级数据
	levels := make([]types.MartingaleLevel, len(bt.MartingaleLevels))
	for i, level := range bt.MartingaleLevels {
		levels[i] = types.MartingaleLevel{
			Level:      level.Level,
			EntryPrice: level.EntryPrice,
			Size:       level.Size,
			Timestamp:  level.Timestamp,
		}
	}

	// 计算胜率
	winRate := 0.0
	if bt.TradeCount > 0 {
		winRate = float64(bt.WinCount) / float64(bt.TradeCount) * 100
	}

	return &types.MartingaleBacktestResponse{
		Success:          true,
		Message:          "回测完成",
		InitialBalance:   initialBalance,
		FinalBalance:     bt.Balance,
		TotalReturn:      (bt.Balance - initialBalance) / initialBalance * 100,
		TradeCount:       bt.TradeCount,
		WinRate:          winRate,
		MaxDrawdown:      bt.MaxDrawdown * 100,
		StopLossCount:    bt.StopLossCount,
		TakeProfitCount:  bt.TakeProfitCount,
		LiquidationCount: bt.LiquidationCount,
		BestParams: types.MartingaleConfigRequest{
			Symbol:              req.Symbol,
			InitialPositionSize: bestParams.InitialPositionSize,
			Multiplier:          bestParams.Multiplier,
			MaxLevels:           bestParams.MaxLevels,
			TakeProfitPercent:   bestParams.TakeProfitPercent,
			MaxLossPercent:      bestParams.MaxLossPercent,
			GridSpacing:         bestParams.GridSpacing,
			UseATRSpacing:       bestParams.UseATRSpacing,
			ATRPeriod:           bestParams.ATRPeriod,
			ATRMultiplier:       bestParams.ATRMultiplier,
		},
		Levels: levels,
	}, nil
}
