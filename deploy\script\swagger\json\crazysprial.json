{"swagger": "2.0", "info": {"title": "sprial服务", "description": "sprial服务", "version": ""}, "host": "**************:8886/", "basePath": "/", "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"crazysprial/record": {"post": {"summary": "spin record", "operationId": "record", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/RecordResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RecordReq"}}], "requestBody": {}, "tags": ["spin"]}}, "crazysprial/spin": {"post": {"summary": "spin", "operationId": "spin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SpinResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SpinReq"}}], "requestBody": {}, "tags": ["spin"]}}, "crazysprial/winners": {"post": {"summary": "winners", "operationId": "winners", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/WinnersResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WinnersReq"}}], "requestBody": {}, "tags": ["spin"]}}}, "definitions": {"Record": {"type": "object", "properties": {"wagerAmount": {"type": "number", "format": "double"}, "won": {"type": "number", "format": "double"}, "dateTime": {"type": "integer", "format": "int64"}}, "title": "Record", "required": ["wagerAmount", "won", "dateTime"]}, "RecordReq": {"type": "object", "title": "RecordReq"}, "RecordResp": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/Record"}}}, "title": "RecordResp", "required": ["records"]}, "SpinReq": {"type": "object", "properties": {"spinMoney": {"type": "number", "format": "double", "description": "下注金额"}}, "title": "SpinReq", "required": ["spinMoney"]}, "SpinResp": {"type": "object", "properties": {"resultIndex": {"type": "integer", "format": "int64", "description": "结果索引"}, "win": {"type": "number", "format": "double", "description": "奖励"}, "money": {"type": "number", "format": "double", "description": "余额"}}, "title": "SpinResp", "required": ["resultIndex", "win", "money"]}, "Winner": {"type": "object", "properties": {"gid": {"type": "integer", "format": "int64"}, "wager": {"type": "number", "format": "double"}, "wining": {"type": "number", "format": "double"}, "spinTime": {"type": "integer", "format": "int64"}}, "title": "Winner", "required": ["gid", "wager", "wining", "spinTime"]}, "WinnersReq": {"type": "object", "title": "WinnersReq"}, "WinnersResp": {"type": "object", "properties": {"winners": {"type": "array", "items": {"$ref": "#/definitions/Winner"}}}, "title": "WinnersResp", "required": ["winners"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}, "security": [{"apiKey": []}]}