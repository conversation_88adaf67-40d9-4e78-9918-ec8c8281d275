package handler

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"zblockchain/app/binance/internal/logic"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"
)

func MartingaleBacktestHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.MartingaleBacktestRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewMartingaleBacktestLogic(r.Context(), svcCtx)
		resp, err := l.MartingaleBacktest(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
