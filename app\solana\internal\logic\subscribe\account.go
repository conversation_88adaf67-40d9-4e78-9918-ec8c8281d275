package subscribe

import (
	"context"
	"zblockchain/app/solana/internal/svc"
	"zblockchain/common/routine"

	"github.com/gagliardetto/solana-go"
	"github.com/zeromicro/go-zero/core/logx"
)

type AccountSubscribe struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAccountSubscribe(ctx context.Context, svcCtx *svc.ServiceContext) *AccountSubscribe {
	return &AccountSubscribe{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *AccountSubscribe) AddAccountSubscribe(pubKey string) error {
	program := solana.MustPublicKeyFromBase58(pubKey) // serum
	return a.subscribe(program)
}

func (a *AccountSubscribe) subscribe(program solana.PublicKey) error{
	err := routine.Run(true, func() {
		sub, err := a.svcCtx.QN_ws_clinet.AccountSubscribe(
			program,
			"",
		)

		if err != nil {
			panic(err)
		  }
		  defer sub.Unsubscribe()
	  
		  for {
			got, err := sub.Recv(a.ctx)
			if err != nil {
			  panic(err)
			}
			logx.Debugf("AccountSubscribe got:%v", got)
		  }
	})
	return err
}
