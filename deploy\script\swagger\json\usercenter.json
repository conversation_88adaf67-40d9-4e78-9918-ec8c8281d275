{"swagger": "2.0", "info": {"title": "用户中心服务", "description": "用户中心服务", "version": ""}, "host": "**************:8886/", "basePath": "/", "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"usercenter/user/detail": {"post": {"summary": "get user info", "operationId": "detail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UserInfoResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserInfoReq"}}], "requestBody": {}, "tags": ["user"]}}, "usercenter/user/login": {"post": {"summary": "login", "operationId": "login", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/LoginResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LoginReq"}}], "requestBody": {}, "tags": ["user"]}}, "usercenter/user/register": {"post": {"summary": "register", "operationId": "register", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/RegisterResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RegisterReq"}}], "requestBody": {}, "tags": ["user"]}}}, "definitions": {"LoginReq": {"type": "object", "properties": {"phone": {"type": "string"}, "password": {"type": "string"}}, "title": "LoginReq", "required": ["phone", "password"]}, "LoginResp": {"type": "object", "properties": {"accessToken": {"type": "string"}, "accessExpire": {"type": "integer", "format": "int64"}}, "title": "LoginResp", "required": ["accessToken", "accessExpire"]}, "RegisterReq": {"type": "object", "properties": {"phone": {"type": "string"}, "password": {"type": "string"}, "spreader": {"type": "integer", "format": "int64"}}, "title": "RegisterReq", "required": ["phone", "password", "spreader"]}, "RegisterResp": {"type": "object", "properties": {"accessToken": {"type": "string"}, "accessExpire": {"type": "integer", "format": "int64"}}, "title": "RegisterResp", "required": ["accessToken", "accessExpire"]}, "User": {"type": "object", "properties": {"gid": {"type": "integer", "format": "int64"}, "money": {"type": "number", "format": "double"}, "phone": {"type": "string"}, "nickname": {"type": "string"}, "gender": {"type": "integer", "format": "int64"}, "head": {"type": "string"}}, "title": "User", "required": ["gid", "money", "phone", "nickname", "gender", "head"]}, "UserInfoReq": {"type": "object", "title": "UserInfoReq"}, "UserInfoResp": {"type": "object", "properties": {"userInfo": {"$ref": "#/definitions/User"}}, "title": "UserInfoResp", "required": ["userInfo"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}, "security": [{"apiKey": []}]}