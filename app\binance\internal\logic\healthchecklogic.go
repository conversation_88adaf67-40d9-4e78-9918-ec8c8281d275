package logic

import (
	"context"
	"time"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type HealthCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHealthCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HealthCheckLogic {
	return &HealthCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HealthCheckLogic) HealthCheck() (resp *types.HealthResponse, err error) {
	return &types.HealthResponse{
		Status:    "ok",
		Timestamp: time.Now().Unix(),
		Version:   "1.0.0",
	}, nil
}
