/*
 Navicat Premium Data Transfer

 Source Server         : ***************
 Source Server Type    : MySQL
 Source Server Version : 80028
 Source Host           : ***************:3306
 Source Schema         : bmgsdk

 Target Server Type    : MySQL
 Target Server Version : 80028
 File Encoding         : 65001

 Date: 05/01/2025 10:33:00
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pay
-- ----------------------------
DROP TABLE IF EXISTS `pay`;
CREATE TABLE `pay`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `openId` varchar(255) NOT NULL DEFAULT '',
  `orderId` varchar(255) NOT NULL DEFAULT '',
  `productId` varchar(255) NOT NULL DEFAULT '',
  `payTime` int(0) NOT NULL DEFAULT 0,
  `price` int(0) NOT NULL DEFAULT 0,
  `status` int(0) NOT NULL DEFAULT 0,
  `createTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `vip`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `openId` varchar(255) NOT NULL DEFAULT "",
  `vipId` int(0) NOT NULL DEFAULT 0,
  `validTime` bigint(0) NOT NULL DEFAULT 0,
  `updateTime` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`)
);

SET FOREIGN_KEY_CHECKS = 1;
