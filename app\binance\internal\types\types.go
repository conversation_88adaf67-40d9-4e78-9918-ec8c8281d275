// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.3

package types

type BreakthroughBacktestRequest struct {
	Symbols     []string `json:"symbols"`
	Interval    string   `json:"interval"`
	StartTime   int64    `json:"startTime"`
	EndTime     int64    `json:"endTime"`
	UseOptimize bool     `json:"useOptimize"`
}

type BreakthroughBacktestResponse struct {
	Success         bool                      `json:"success"`
	Message         string                    `json:"message"`
	InitialBalance  float64                   `json:"initialBalance"`
	FinalBalance    float64                   `json:"finalBalance"`
	TotalReturn     float64                   `json:"totalReturn"`
	TradeCount      int                       `json:"tradeCount"`
	WinRate         float64                   `json:"winRate"`
	MaxDrawdown     float64                   `json:"maxDrawdown"`
	DailyPnL        float64                   `json:"dailyPnL"`
	TotalPnL        float64                   `json:"totalPnL"`
	BestParams      BreakthroughConfigRequest `json:"bestParams"`
	Positions       []BreakthroughPosition    `json:"positions"`
	ActivePositions int                       `json:"activePositions"`
}

type BreakthroughConfigRequest struct {
	EMAFast           int     `json:"emaFast"`
	EMASlow           int     `json:"emaSlow"`
	BollingerPeriod   int     `json:"bollingerPeriod"`
	BollingerStdDev   float64 `json:"bollingerStdDev"`
	BreakoutThreshold float64 `json:"breakoutThreshold"`
	TakeProfitMin     float64 `json:"takeProfitMin"`
	TakeProfitMax     float64 `json:"takeProfitMax"`
	StopLossMin       float64 `json:"stopLossMin"`
	StopLossMax       float64 `json:"stopLossMax"`
	PositionSize      float64 `json:"positionSize"`
	LeverageDefault   float64 `json:"leverageDefault"`
	LeverageMax       float64 `json:"leverageMax"`
	MaxPositions      int     `json:"maxPositions"`
	DailyTarget       float64 `json:"dailyTarget"`
	MaxDrawdown       float64 `json:"maxDrawdown"`
	VolumeMultiplier  float64 `json:"volumeMultiplier"`
	VolumePeriod      int     `json:"volumePeriod"`
	MomentumPeriod    int     `json:"momentumPeriod"`
	InitialBalance    float64 `json:"initialBalance"`
}

type BreakthroughConfigResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type BreakthroughPosition struct {
	ID         int     `json:"id"`
	Symbol     string  `json:"symbol"`
	Direction  string  `json:"direction"`
	EntryPrice float64 `json:"entryPrice"`
	Size       float64 `json:"size"`
	Leverage   float64 `json:"leverage"`
	TakeProfit float64 `json:"takeProfit"`
	StopLoss   float64 `json:"stopLoss"`
	Timestamp  int64   `json:"timestamp"`
	IsActive   bool    `json:"isActive"`
}

type BreakthroughStatusResponse struct {
	IsRunning        bool                   `json:"isRunning"`
	CurrentPositions []BreakthroughPosition `json:"currentPositions"`
	DailyPnL         float64                `json:"dailyPnL"`
	TotalPnL         float64                `json:"totalPnL"`
	DailyTarget      float64                `json:"dailyTarget"`
	MaxDrawdown      float64                `json:"maxDrawdown"`
	ActivePositions  int                    `json:"activePositions"`
	CurrentBalance   float64                `json:"currentBalance"`
}

type HealthResponse struct {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
	Version   string `json:"version"`
}

type KlineData struct {
	OpenTime  int64   `json:"openTime"`
	Open      float64 `json:"open"`
	High      float64 `json:"high"`
	Low       float64 `json:"low"`
	Close     float64 `json:"close"`
	Volume    float64 `json:"volume"`
	CloseTime int64   `json:"closeTime"`
}

type KlineRequest struct {
	Symbol    string `form:"symbol"`
	Interval  string `form:"interval"`
	StartTime int64  `form:"startTime,optional"`
	EndTime   int64  `form:"endTime,optional"`
	Limit     int    `form:"limit,optional"`
}

type KlineResponse struct {
	Symbol string      `json:"symbol"`
	Data   []KlineData `json:"data"`
}

type MartingaleBacktestRequest struct {
	Symbol        string `json:"symbol"`
	Interval      string `json:"interval"`
	StartTime     int64  `json:"startTime"`
	EndTime       int64  `json:"endTime"`
	UseOptimize   bool   `json:"useOptimize"`
	QuickOptimize bool   `json:"quickOptimize"` // 快速优化，仅测试3个预设策略
}

type MartingaleBacktestResponse struct {
	Success          bool                    `json:"success"`
	Message          string                  `json:"message"`
	InitialBalance   float64                 `json:"initialBalance"`
	FinalBalance     float64                 `json:"finalBalance"`
	TotalReturn      float64                 `json:"totalReturn"`
	TradeCount       int                     `json:"tradeCount"`
	WinRate          float64                 `json:"winRate"`
	MaxDrawdown      float64                 `json:"maxDrawdown"`
	StopLossCount    int                     `json:"stopLossCount"`
	TakeProfitCount  int                     `json:"takeProfitCount"`
	LiquidationCount int                     `json:"liquidationCount"`
	BestParams       MartingaleConfigRequest `json:"bestParams"`
	Levels           []MartingaleLevel       `json:"levels"`
}

type MartingaleConfigRequest struct {
	Symbol              string  `json:"symbol"`
	InitialPositionSize float64 `json:"initialPositionSize"`
	Multiplier          float64 `json:"multiplier"`
	MaxLevels           int     `json:"maxLevels"`
	TakeProfitPercent   float64 `json:"takeProfitPercent"`
	MaxLossPercent      float64 `json:"maxLossPercent"`
	GridSpacing         float64 `json:"gridSpacing"`
	UseATRSpacing       bool    `json:"useATRSpacing"`
	ATRPeriod           int     `json:"atrPeriod"`
	ATRMultiplier       float64 `json:"atrMultiplier"`
}

type MartingaleConfigResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type MartingaleLevel struct {
	Level      int     `json:"level"`
	EntryPrice float64 `json:"entryPrice"`
	Size       float64 `json:"size"`
	Timestamp  int64   `json:"timestamp"`
}

type MartingaleStatusResponse struct {
	IsRunning       bool              `json:"isRunning"`
	CurrentLevels   []MartingaleLevel `json:"currentLevels"`
	TotalPnL        float64           `json:"totalPnL"`
	TotalInvestment float64           `json:"totalInvestment"`
	CurrentPrice    float64           `json:"currentPrice"`
}

type OrderRequest struct {
	Symbol   string  `json:"symbol"`
	Side     string  `json:"side"` // BUY or SELL
	Type     string  `json:"type"` // LIMIT, MARKET, etc.
	Quantity float64 `json:"quantity"`
	Price    float64 `json:"price"`
}

type OrderResponse struct {
	OrderId int64  `json:"orderId"`
	Symbol  string `json:"symbol"`
	Status  string `json:"status"`
}

type PriceRequest struct {
	Symbol string `path:"symbol"`
}

type PriceResponse struct {
	Symbol string  `json:"symbol"`
	Price  float64 `json:"price"`
}

type TrendFollowBacktestRequest struct {
	Symbol      string `json:"symbol"`
	Interval    string `json:"interval"`
	StartTime   int64  `json:"startTime"`
	EndTime     int64  `json:"endTime"`
	UseOptimize bool   `json:"useOptimize"`
}

type TrendFollowBacktestResponse struct {
	Success         bool                     `json:"success"`
	Message         string                   `json:"message"`
	InitialBalance  float64                  `json:"initialBalance"`
	FinalBalance    float64                  `json:"finalBalance"`
	TotalReturn     float64                  `json:"totalReturn"`
	TradeCount      int                      `json:"tradeCount"`
	WinRate         float64                  `json:"winRate"`
	MaxDrawdown     float64                  `json:"maxDrawdown"`
	StopLossCount   int                      `json:"stopLossCount"`
	TakeProfitCount int                      `json:"takeProfitCount"`
	BestParams      TrendFollowConfigRequest `json:"bestParams"`
	Positions       []TrendFollowPosition    `json:"positions"`
}

type TrendFollowConfigRequest struct {
	Symbol               string  `json:"symbol"`
	EMAFast              int     `json:"emaFast"`
	EMASlow              int     `json:"emaSlow"`
	InitialPositionRatio float64 `json:"initialPositionRatio"`
	CallbackPercent      float64 `json:"callbackPercent"`
	MaxAddPositions      int     `json:"maxAddPositions"`
	MaxPositionRatio     float64 `json:"maxPositionRatio"`
	TakeProfitPercent    float64 `json:"takeProfitPercent"`
	StopLossPercent      float64 `json:"stopLossPercent"`
	RSILowerBound        float64 `json:"rsiLowerBound"`
	RSIUpperBound        float64 `json:"rsiUpperBound"`
	RSIExitBound         float64 `json:"rsiExitBound"`
	RSIPeriod            int     `json:"rsiPeriod"`
	VolumeMultiplier     float64 `json:"volumeMultiplier"`
	VolumePeriod         int     `json:"volumePeriod"`
	UpperShadowRatio     float64 `json:"upperShadowRatio"`
}

type TrendFollowConfigResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type TrendFollowPosition struct {
	Level      int     `json:"level"`
	EntryPrice float64 `json:"entryPrice"`
	Size       float64 `json:"size"`
	Timestamp  int64   `json:"timestamp"`
	IsActive   bool    `json:"isActive"`
}

type TrendFollowStatusResponse struct {
	IsRunning        bool                  `json:"isRunning"`
	CurrentPositions []TrendFollowPosition `json:"currentPositions"`
	TotalPnL         float64               `json:"totalPnL"`
	TotalInvestment  float64               `json:"totalInvestment"`
	CurrentPrice     float64               `json:"currentPrice"`
	AvgEntryPrice    float64               `json:"avgEntryPrice"`
}
