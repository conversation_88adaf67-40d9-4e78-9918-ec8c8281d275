package logic

import (
	"sync"
	"zblockchain/common/routine"
)

// ParamSet 参数组合
type ParamSet struct {
	Multiplier        float64
	MaxLevels         int
	TakeProfitPercent float64
	MaxLossPercent    float64
	GridSpacing       float64
	UseATRSpacing     bool
	ATRPeriod         int
	ATRMultiplier     float64
}

// OptimizationResult 优化结果
type OptimizationResult struct {
	Params  MartingaleParams
	Balance float64
	Score   float64 // 综合评分
}

// RSIOptimizationResult RSI优化结果
type RSIOptimizationResult struct {
	Params  RSIParams
	Balance float64
	Score   float64 // 综合评分
}

type StrategyFunc func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64)

// OptimizeRSIParameters 优化RSI策略参数 - 高效版本
func OptimizeRSIParameters(klines []*Kline, fundingRates []*FundingRate, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate float64) (RSIParams, float64) {
	// 使用预定义的高质量参数组合，避免暴力搜索
	paramCombinations := []RSIParams{
		// 保守型策略 - 适合震荡市场
		{
			Period:               14,
			BuyThreshold:         25,
			SellThreshold:        75,
			StopLossMultiplier:   1.5,
			TakeProfitMultiplier: 2.0,
		},
		// 平衡型策略 - 通用策略
		{
			Period:               14,
			BuyThreshold:         30,
			SellThreshold:        70,
			StopLossMultiplier:   2.0,
			TakeProfitMultiplier: 2.5,
		},
		// 激进型策略 - 适合趋势市场
		{
			Period:               10,
			BuyThreshold:         20,
			SellThreshold:        80,
			StopLossMultiplier:   1.0,
			TakeProfitMultiplier: 3.0,
		},
		// 短周期策略 - 适合高频交易
		{
			Period:               7,
			BuyThreshold:         25,
			SellThreshold:        75,
			StopLossMultiplier:   1.5,
			TakeProfitMultiplier: 2.0,
		},
		// 长周期策略 - 适合长期持有
		{
			Period:               21,
			BuyThreshold:         30,
			SellThreshold:        70,
			StopLossMultiplier:   2.5,
			TakeProfitMultiplier: 3.0,
		},
	}

	bestParams := paramCombinations[1] // 默认使用平衡型策略
	bestBalance := initialBalance
	bestScore := 0.0

	// 使用项目统一的routine管理器并发测试所有参数组合
	results := make(chan RSIOptimizationResult, len(paramCombinations))
	var wg sync.WaitGroup
	successCount := 0 // 记录成功提交的任务数量

	for _, params := range paramCombinations {
		wg.Add(1)
		p := params // 避免闭包问题

		// 使用common/routine管理goroutine
		err := routine.Run(true, func() {
			defer wg.Done()

			bt := NewBacktest(initialBalance, feeRate, leverage, p.StopLossMultiplier, p.TakeProfitMultiplier, maintenanceMarginRate)
			bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
				return RSIStrategy(klines, bt, index, p)
			})

			// 计算综合评分 (收益率 - 风险惩罚 + 胜率奖励)
			returnRate := (bt.Balance - initialBalance) / initialBalance
			riskPenalty := bt.MaxDrawdown * 1.5 // RSI策略风险惩罚相对较小
			winRateBonus := 0.0
			if bt.TradeCount > 0 {
				winRate := float64(bt.WinCount) / float64(bt.TradeCount)
				if winRate > 0.6 { // 胜率超过60%给予奖励
					winRateBonus = (winRate - 0.6) * 0.5
				}
			}
			score := returnRate - riskPenalty + winRateBonus

			results <- RSIOptimizationResult{
				Params:  p,
				Balance: bt.Balance,
				Score:   score,
			}
		})

		if err != nil {
			// 协程提交失败，记录错误并跳过该参数组合
			wg.Done()
			// 可以在这里记录日志：协程池可能已满或其他错误
		} else {
			successCount++ // 记录成功提交的任务数量
		}
	}

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果 - 只收集成功执行的结果
	resultCount := 0
	for result := range results {
		resultCount++
		if result.Score > bestScore {
			bestScore = result.Score
			bestBalance = result.Balance
			bestParams = result.Params
		}
	}

	// 如果没有任何成功的结果，返回默认参数
	if resultCount == 0 {
		bestParams = GetDefaultRSIParams()
		// 使用默认参数运行一次回测获取余额
		bt := NewBacktest(initialBalance, feeRate, leverage, bestParams.StopLossMultiplier, bestParams.TakeProfitMultiplier, maintenanceMarginRate)
		bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
			return RSIStrategy(klines, bt, index, bestParams)
		})
		bestBalance = bt.Balance
	}

	return bestParams, bestBalance
}

// QuickOptimizeRSIParameters 快速优化RSI策略参数 - 仅测试3个预设策略
func QuickOptimizeRSIParameters(klines []*Kline, fundingRates []*FundingRate, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate float64) (RSIParams, float64) {
	// 只测试3个经过验证的策略组合
	paramCombinations := []RSIParams{
		// 保守型策略 - 适合震荡市场
		{
			Period:               14,
			BuyThreshold:         25,
			SellThreshold:        75,
			StopLossMultiplier:   1.5,
			TakeProfitMultiplier: 2.0,
		},
		// 平衡型策略 - 通用策略
		{
			Period:               14,
			BuyThreshold:         30,
			SellThreshold:        70,
			StopLossMultiplier:   2.0,
			TakeProfitMultiplier: 2.5,
		},
		// 激进型策略 - 适合趋势市场
		{
			Period:               10,
			BuyThreshold:         20,
			SellThreshold:        80,
			StopLossMultiplier:   1.0,
			TakeProfitMultiplier: 3.0,
		},
	}

	bestParams := paramCombinations[1] // 默认使用平衡型策略
	bestBalance := initialBalance

	// 顺序测试，避免并发开销
	for _, params := range paramCombinations {
		bt := NewBacktest(initialBalance, feeRate, leverage, params.StopLossMultiplier, params.TakeProfitMultiplier, maintenanceMarginRate)
		bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
			return RSIStrategy(klines, bt, index, params)
		})

		if bt.Balance > bestBalance {
			bestBalance = bt.Balance
			bestParams = params
		}
	}

	return bestParams, bestBalance
}

// GetDefaultRSIParams 获取默认RSI策略参数 - 无需优化，直接使用
func GetDefaultRSIParams() RSIParams {
	return RSIParams{
		Period:               14,
		BuyThreshold:         30,
		SellThreshold:        70,
		StopLossMultiplier:   2.0,
		TakeProfitMultiplier: 2.5,
	}
}

// OptimizeMartingaleParameters 优化马丁策略参数 - 高效版本
func OptimizeMartingaleParameters(klines []*Kline, fundingRates []*FundingRate, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate float64) (MartingaleParams, float64) {
	// 使用预定义的高质量参数组合，避免暴力搜索
	paramCombinations := []MartingaleParams{
		// 保守型策略
		{
			InitialPositionSize: initialBalance * 0.05,
			Multiplier:          1.5,
			MaxLevels:           3,
			TakeProfitPercent:   0.02,
			MaxLossPercent:      0.1,
			GridSpacing:         0.015,
			UseATRSpacing:       false,
		},
		// 平衡型策略
		{
			InitialPositionSize: initialBalance * 0.1,
			Multiplier:          2.0,
			MaxLevels:           5,
			TakeProfitPercent:   0.03,
			MaxLossPercent:      0.15,
			GridSpacing:         0.02,
			UseATRSpacing:       false,
		},
		// 激进型策略
		{
			InitialPositionSize: initialBalance * 0.15,
			Multiplier:          2.5,
			MaxLevels:           7,
			TakeProfitPercent:   0.05,
			MaxLossPercent:      0.2,
			GridSpacing:         0.025,
			UseATRSpacing:       false,
		},
		// ATR动态间距策略
		{
			InitialPositionSize: initialBalance * 0.1,
			Multiplier:          2.0,
			MaxLevels:           5,
			TakeProfitPercent:   0.03,
			MaxLossPercent:      0.15,
			GridSpacing:         0.02,
			UseATRSpacing:       true,
			ATRPeriod:           14,
			ATRMultiplier:       1.5,
		},
		// 高频小利策略
		{
			InitialPositionSize: initialBalance * 0.08,
			Multiplier:          1.8,
			MaxLevels:           4,
			TakeProfitPercent:   0.015,
			MaxLossPercent:      0.12,
			GridSpacing:         0.01,
			UseATRSpacing:       false,
		},
	}

	bestParams := paramCombinations[1] // 默认使用平衡型策略
	bestBalance := initialBalance
	bestScore := 0.0

	// 使用项目统一的routine管理器并发测试所有参数组合
	results := make(chan OptimizationResult, len(paramCombinations))
	var wg sync.WaitGroup
	successCount := 0 // 记录成功提交的任务数量

	for _, params := range paramCombinations {
		wg.Add(1)
		p := params // 避免闭包问题

		// 使用common/routine管理goroutine
		err := routine.Run(true, func() {
			defer wg.Done()

			bt := NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
			bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
				return MartingaleStrategy(klines, bt, index, p)
			})

			// 计算综合评分 (收益率 - 风险惩罚)
			returnRate := (bt.Balance - initialBalance) / initialBalance
			riskPenalty := bt.MaxDrawdown * 2 // 回撤惩罚
			score := returnRate - riskPenalty

			results <- OptimizationResult{
				Params:  p,
				Balance: bt.Balance,
				Score:   score,
			}
		})

		if err != nil {
			// 协程提交失败，记录错误并跳过该参数组合
			wg.Done()
			// 可以在这里记录日志：协程池可能已满或其他错误
			// 不影响其他参数组合的测试，继续处理下一个
		} else {
			successCount++ // 记录成功提交的任务数量
		}
	}

	// 等待所有goroutine完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果 - 只收集成功执行的结果
	resultCount := 0
	for result := range results {
		resultCount++
		if result.Score > bestScore {
			bestScore = result.Score
			bestBalance = result.Balance
			bestParams = result.Params
		}
	}

	// 如果没有任何成功的结果，返回默认参数
	if resultCount == 0 {
		bestParams = GetDefaultMartingaleParams(initialBalance)
		// 使用默认参数运行一次回测获取余额
		bt := NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
		bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
			return MartingaleStrategy(klines, bt, index, bestParams)
		})
		bestBalance = bt.Balance
	}

	return bestParams, bestBalance
}

// QuickOptimizeMartingaleParameters 快速优化马丁策略参数 - 仅测试3个预设策略
func QuickOptimizeMartingaleParameters(klines []*Kline, fundingRates []*FundingRate, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate float64) (MartingaleParams, float64) {
	// 只测试3个经过验证的策略组合
	paramCombinations := []MartingaleParams{
		// 保守型策略 - 适合震荡市场
		{
			InitialPositionSize: initialBalance * 0.05,
			Multiplier:          1.5,
			MaxLevels:           3,
			TakeProfitPercent:   0.02,
			MaxLossPercent:      0.1,
			GridSpacing:         0.015,
			UseATRSpacing:       false,
		},
		// 平衡型策略 - 通用策略
		{
			InitialPositionSize: initialBalance * 0.1,
			Multiplier:          2.0,
			MaxLevels:           5,
			TakeProfitPercent:   0.03,
			MaxLossPercent:      0.15,
			GridSpacing:         0.02,
			UseATRSpacing:       false,
		},
		// 激进型策略 - 适合趋势反转
		{
			InitialPositionSize: initialBalance * 0.15,
			Multiplier:          2.5,
			MaxLevels:           7,
			TakeProfitPercent:   0.05,
			MaxLossPercent:      0.2,
			GridSpacing:         0.025,
			UseATRSpacing:       false,
		},
	}

	bestParams := paramCombinations[1] // 默认使用平衡型策略
	bestBalance := initialBalance

	// 顺序测试，避免并发开销
	for _, params := range paramCombinations {
		bt := NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
		bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
			return MartingaleStrategy(klines, bt, index, params)
		})

		if bt.Balance > bestBalance {
			bestBalance = bt.Balance
			bestParams = params
		}
	}

	return bestParams, bestBalance
}

// GetDefaultMartingaleParams 获取默认马丁策略参数 - 无需优化，直接使用
func GetDefaultMartingaleParams(initialBalance float64) MartingaleParams {
	return MartingaleParams{
		InitialPositionSize: initialBalance * 0.1,
		Multiplier:          2.0,
		MaxLevels:           5,
		TakeProfitPercent:   0.03,
		MaxLossPercent:      0.15,
		GridSpacing:         0.02,
		UseATRSpacing:       false,
		ATRPeriod:           14,
		ATRMultiplier:       1.0,
	}
}

// OptimizeTrendFollowParameters 优化趋势跟随策略参数
func OptimizeTrendFollowParameters(klines []*Kline, fundingRates []*FundingRate, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate float64) (TrendFollowParams, float64) {
	// 预定义的高质量参数组合
	paramCombinations := []TrendFollowParams{
		// 保守型策略 - 适合震荡市场
		{
			EMAFast:              5,
			EMASlow:              20,
			InitialPositionRatio: 0.15,
			CallbackPercent:      0.015,
			MaxAddPositions:      1,
			MaxPositionRatio:     0.45,
			TakeProfitPercent:    0.04,
			StopLossPercent:      0.01,
			RSILowerBound:        45,
			RSIUpperBound:        65,
			RSIExitBound:         75,
			RSIPeriod:            14,
			VolumeMultiplier:     1.1,
			VolumePeriod:         5,
			UpperShadowRatio:     0.25,
		},
		// 平衡型策略 - 通用策略
		{
			EMAFast:              5,
			EMASlow:              20,
			InitialPositionRatio: 0.2,
			CallbackPercent:      0.02,
			MaxAddPositions:      2,
			MaxPositionRatio:     0.6,
			TakeProfitPercent:    0.06,
			StopLossPercent:      0.015,
			RSILowerBound:        40,
			RSIUpperBound:        70,
			RSIExitBound:         80,
			RSIPeriod:            14,
			VolumeMultiplier:     1.2,
			VolumePeriod:         5,
			UpperShadowRatio:     0.3,
		},
		// 激进型策略 - 适合强趋势
		{
			EMAFast:              5,
			EMASlow:              20,
			InitialPositionRatio: 0.25,
			CallbackPercent:      0.025,
			MaxAddPositions:      3,
			MaxPositionRatio:     0.75,
			TakeProfitPercent:    0.08,
			StopLossPercent:      0.02,
			RSILowerBound:        35,
			RSIUpperBound:        75,
			RSIExitBound:         85,
			RSIPeriod:            14,
			VolumeMultiplier:     1.3,
			VolumePeriod:         5,
			UpperShadowRatio:     0.35,
		},
	}

	bestParams := paramCombinations[1] // 默认使用平衡型策略
	bestBalance := initialBalance

	// 顺序测试，避免并发开销
	for _, params := range paramCombinations {
		bt := NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
		bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
			return TrendFollowStrategy(klines, bt, index, params)
		})

		if bt.Balance > bestBalance {
			bestBalance = bt.Balance
			bestParams = params
		}
	}

	return bestParams, bestBalance
}

// OptimizeBreakthroughParameters 优化双向突破策略参数
func OptimizeBreakthroughParameters(klines []*Kline, fundingRates []*FundingRate, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate float64) (BreakthroughParams, float64) {
	// 预定义的高质量参数组合
	paramCombinations := []BreakthroughParams{
		// 保守型策略 - 适合震荡市场
		{
			EMAFast:           5,
			EMASlow:           20,
			BollingerPeriod:   20,
			BollingerStdDev:   2.0,
			BreakoutThreshold: 0.003, // 0.3%
			TakeProfitMin:     0.008, // 0.8%
			TakeProfitMax:     0.012, // 1.2%
			StopLossMin:       0.004, // 0.4%
			StopLossMax:       0.006, // 0.6%
			PositionSize:      40.0,  // 40U
			LeverageDefault:   15.0,  // 15x
			LeverageMax:       30.0,  // 30x
			MaxPositions:      2,     // 最多2个仓位
			DailyTarget:       80.0,  // 80U日收益目标
			MaxDrawdown:       80.0,  // 80U最大回撤
			VolumeMultiplier:  1.1,   // 成交量1.1倍
			VolumePeriod:      5,     // 5周期成交量
			MomentumPeriod:    8,     // 8周期动量
			InitialBalance:    500.0, // 500U初始资金
		},
		// 平衡型策略 - 通用策略
		{
			EMAFast:           5,
			EMASlow:           20,
			BollingerPeriod:   20,
			BollingerStdDev:   2.0,
			BreakoutThreshold: 0.005, // 0.5%
			TakeProfitMin:     0.01,  // 1.0%
			TakeProfitMax:     0.015, // 1.5%
			StopLossMin:       0.005, // 0.5%
			StopLossMax:       0.008, // 0.8%
			PositionSize:      50.0,  // 50U
			LeverageDefault:   20.0,  // 20x
			LeverageMax:       50.0,  // 50x
			MaxPositions:      3,     // 最多3个仓位
			DailyTarget:       100.0, // 100U日收益目标
			MaxDrawdown:       100.0, // 100U最大回撤
			VolumeMultiplier:  1.2,   // 成交量1.2倍
			VolumePeriod:      5,     // 5周期成交量
			MomentumPeriod:    10,    // 10周期动量
			InitialBalance:    500.0, // 500U初始资金
		},
		// 激进型策略 - 适合高波动
		{
			EMAFast:           5,
			EMASlow:           20,
			BollingerPeriod:   20,
			BollingerStdDev:   2.0,
			BreakoutThreshold: 0.007, // 0.7%
			TakeProfitMin:     0.012, // 1.2%
			TakeProfitMax:     0.02,  // 2.0%
			StopLossMin:       0.006, // 0.6%
			StopLossMax:       0.01,  // 1.0%
			PositionSize:      60.0,  // 60U
			LeverageDefault:   25.0,  // 25x
			LeverageMax:       100.0, // 100x
			MaxPositions:      4,     // 最多4个仓位
			DailyTarget:       120.0, // 120U日收益目标
			MaxDrawdown:       120.0, // 120U最大回撤
			VolumeMultiplier:  1.3,   // 成交量1.3倍
			VolumePeriod:      5,     // 5周期成交量
			MomentumPeriod:    12,    // 12周期动量
			InitialBalance:    500.0, // 500U初始资金
		},
	}

	bestParams := paramCombinations[1] // 默认使用平衡型策略
	bestBalance := initialBalance

	// 顺序测试，避免并发开销（双向突破策略更复杂）
	for _, params := range paramCombinations {
		bt := NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
		bt.RunBreakthrough(klines, fundingRates, params, func(klines []*Kline, bt *Backtest, index int, params BreakthroughParams, symbol string, allSymbolPrices map[string]float64) (TradeSignal, float64, string) {
			return BreakthroughStrategy(klines, bt, index, params, symbol, allSymbolPrices)
		})

		// 综合评分：考虑收益、风险和达标情况
		returnRate := (bt.Balance - initialBalance) / initialBalance
		riskPenalty := bt.MaxDrawdown * 2.0 // 双向突破策略风险惩罚

		// 如果达到每日目标，给予额外奖励
		targetBonus := 0.0
		if bt.BreakthroughDailyPnL >= params.DailyTarget {
			targetBonus = 0.1 // 10%奖励
		}

		score := returnRate - riskPenalty + targetBonus

		if score > (bestBalance-initialBalance)/initialBalance {
			bestBalance = bt.Balance
			bestParams = params
		}
	}

	return bestParams, bestBalance
}
