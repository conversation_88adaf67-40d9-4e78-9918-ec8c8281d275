package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"
	"zblockchain/app/binance/logic"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPriceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPriceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPriceLogic {
	return &GetPriceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPriceLogic) GetPrice(req *types.PriceRequest) (resp *types.PriceResponse, err error) {
	url := fmt.Sprintf("%s/api/v3/ticker/price?symbol=%s", l.svcCtx.Config.BaseUrl.Http, req.Symbol)

	client := &http.Client{Timeout: 10 * time.Second}
	httpReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	httpResp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer httpResp.Body.Close()
	logic.Run()
	var result struct {
		Symbol string `json:"symbol"`
		Price  string `json:"price"`
	}
	if err := json.NewDecoder(httpResp.Body).Decode(&result); err != nil {
		return nil, err
	}

	price := 0.0
	fmt.Sscanf(result.Price, "%f", &price)

	// if err := routine.Run(true, logic.Run); err != nil {
	// 	panic(err)
	// }

	return &types.PriceResponse{
		Symbol: result.Symbol,
		Price:  price,
	}, nil
}
