package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ PayModel = (*customPayModel)(nil)

type (
	// PayModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPayModel.
	PayModel interface {
		payModel
		RowBuilder() squirrel.SelectBuilder
		FindOneByQuery(ctx context.Context, rowBuilder squirrel.SelectBuilder) (*Pay, error)
		withSession(session sqlx.Session) PayModel
	}

	customPayModel struct {
		*defaultPayModel
	}
)

// NewPayModel returns a model for the database table.
func NewPayModel(conn sqlx.SqlConn) PayModel {
	return &customPayModel{
		defaultPayModel: newPayModel(conn),
	}
}

func (m *customPayModel) withSession(session sqlx.Session) PayModel {
	return NewPayModel(sqlx.NewSqlConnFromSession(session))
}

func (m *defaultPayModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(payRows).From(m.table)
}

func (m *defaultPayModel) FindOneByQuery(ctx context.Context, rowBuilder squirrel.SelectBuilder) (*Pay, error) {

	query, values, err := rowBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp Pay
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}