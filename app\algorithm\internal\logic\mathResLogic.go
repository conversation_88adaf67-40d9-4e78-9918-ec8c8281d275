package logic

import (
	"context"

	"zblockchain/app/algorithm/internal/svc"
	"zblockchain/app/algorithm/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type MathResLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewMathResLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MathResLogic {
	return &MathResLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *MathResLogic) MathRes(in *pb.MathReq) (*pb.MathResp, error) {
	// todo: add your logic here and delete this line

	return &pb.MathResp{}, nil
}
