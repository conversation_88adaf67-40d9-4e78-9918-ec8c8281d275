package agent

import (
	"zblockchain/common/network/core"

	"github.com/zeromicro/go-zero/core/logx"
)

// SendAgent 发消息代理，一连上就开始发消息
type SendAgent struct {
	SingleAgent
}

func GetSendAgent() core.Agent {
	return new(SendAgent)
}

func (agent *SendAgent) OnConnect(conn core.Conn) {
	_, err := conn.Write([]byte("hello world"))
	if err != nil {
		logx.Error("connection write msg error: %s", err.Error())
	}
}
