package dal

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	instance Storage
)

func Init(redis *redis.Redis, db sqlx.SqlConn) Storage {
	if instance == nil {
		instance = &storage{
			redis: redis,
			db:    db,
		}
	}
	return instance
}

func GetInstance() Storage {
	return instance
}

type storage struct {
	redis *redis.Redis
	db    sqlx.SqlConn
}

func (s *storage) DB() sqlx.SqlConn {
	return s.db
}

// Redis操作实现
func (s *storage) Redis() *redis.Redis {
	return s.redis
}

func (s *storage) Set(key string, value string) error {
	err := s.redis.Set(key, value)
	if err != nil {
		logx.Errorf("Storage Redis Set[%s] Error:%s", key, err.Error())
	}
	return err
}

func (s *storage) HSet(key, field, value string) error {
	err := s.redis.Hset(key, field, value)
	if err != nil {
		logx.Errorf("Storage Redis HSet[%s] Error:%s", key, err.Error())
	}
	return err
}

func (s *storage) Get(key string) (string, error) {
	val, err := s.redis.Get(key)
	if err != nil {
		logx.Errorf("Storage Redis Get[%s] Error:%s", key, err.Error())
	}
	return val, err
}

func (s *storage) HMSet(key string, value map[string]string) error {
	err := s.redis.Hmset(key, value)
	if err != nil {
		logx.Errorf("Storage Redis Set[%s] Error:%s", key, err.Error())
	}
	return err
}

func (s *storage) HGet(key, field string) string {
	val, err := s.redis.Hget(key, field)
	if err != nil {
		logx.Errorf("Storage Redis HGet[%s] Error:%s", key, err.Error())
		return ""
	}
	return val
}

func (s *storage) HMGet(key string, fields ...string) []string {
	val, err := s.redis.Hmget(key, fields...)
	if err != nil {
		logx.Errorf("Storage Redis HMGet[%s] Error:%s", key, err.Error())
		return nil
	}
	return val
}

func (s *storage) HGetAll(key string) map[string]string {
	val, err := s.redis.Hgetall(key)
	if err != nil {
		logx.Errorf("Storage Redis HGetAll[%s] Error:%s", key, err.Error())
		return nil
	}
	return val
}

func (s *storage) HKeys(key string) []string {
	val, err := s.redis.Hkeys(key)
	if err != nil {
		logx.Errorf("Storage Redis HKeys[%s] Error:%s", key, err.Error())
		return []string{}
	}
	return val
}

func (s *storage) Del(key string) error {
	_, err := s.redis.Del(key)
	if err != nil {
		logx.Errorf("Storage Redis Del[%s] Error:%s", key, err.Error())
	}
	return err
}

func (s *storage) HDel(key, field string) error {
	_, err := s.redis.Hdel(key, field)
	if err != nil {
		logx.Errorf("Storage Redis Hdel[%s] Error:%s", key, err.Error())
	}
	return err
}

func (s *storage) Exists(key string) bool {
	exists, err := s.redis.Exists(key)
	if err != nil {
		return false
	}
	return exists
}

func (s *storage) Expire(key string, seconds int) error {
	err := s.redis.Expire(key, seconds)
	return err
}
