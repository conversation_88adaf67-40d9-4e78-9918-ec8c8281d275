package logic

import (
	"context"
	"fmt"

	"zblockchain/app/binance/config"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"
	binanceLogic "zblockchain/app/binance/logic"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/logx"
)

type TrendFollowBacktestLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTrendFollowBacktestLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TrendFollowBacktestLogic {
	return &TrendFollowBacktestLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TrendFollowBacktestLogic) TrendFollowBacktest(req *types.TrendFollowBacktestRequest) (resp *types.TrendFollowBacktestResponse, err error) {
	// 配置币安 API
	client := futures.NewClient(config.GlobalConfig.ApiKey, config.GlobalConfig.ApiSecret)

	// 设置回测参数
	leverage := 2.0 // 趋势跟随策略建议使用1-2倍杠杆
	initialBalance := 10000.0
	feeRate := 0.04 / 100
	stopLossMultiplier := 1.5
	takeProfitMultiplier := 2.0
	maintenanceMarginRate := 0.005

	// 获取历史 K 线数据
	klines, err := binanceLogic.GetHistoricalKlines(client, req.Symbol, req.Interval, req.StartTime, req.EndTime)
	if err != nil {
		return &types.TrendFollowBacktestResponse{
			Success: false,
			Message: fmt.Sprintf("获取 K 线数据失败: %v", err),
		}, nil
	}

	// 获取历史资金费率
	fundingRates, err := binanceLogic.GetHistoricalFundingRates(client, req.Symbol, req.StartTime, req.EndTime)
	if err != nil {
		return &types.TrendFollowBacktestResponse{
			Success: false,
			Message: fmt.Sprintf("获取资金费率失败: %v", err),
		}, nil
	}

	var bestParams binanceLogic.TrendFollowParams

	if req.UseOptimize {
		// 运行参数优化
		bestParams, _ = binanceLogic.OptimizeTrendFollowParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	} else {
		// 使用默认参数
		bestParams = binanceLogic.GetDefaultTrendFollowParams(initialBalance)
	}

	// 使用最佳参数运行回测
	bt := binanceLogic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt.Run(klines, fundingRates, func(klines []*binanceLogic.Kline, bt *binanceLogic.Backtest, index int) (binanceLogic.TradeSignal, float64) {
		return binanceLogic.TrendFollowStrategy(klines, bt, index, bestParams)
	})

	// 转换趋势跟随持仓数据
	positions := make([]types.TrendFollowPosition, len(bt.TrendFollowPositions))
	for i, pos := range bt.TrendFollowPositions {
		positions[i] = types.TrendFollowPosition{
			Level:      pos.Level,
			EntryPrice: pos.EntryPrice,
			Size:       pos.Size,
			Timestamp:  pos.Timestamp,
			IsActive:   pos.IsActive,
		}
	}

	// 计算胜率
	winRate := 0.0
	if bt.TradeCount > 0 {
		winRate = float64(bt.WinCount) / float64(bt.TradeCount) * 100
	}

	return &types.TrendFollowBacktestResponse{
		Success:         true,
		Message:         "趋势跟随策略回测完成",
		InitialBalance:  initialBalance,
		FinalBalance:    bt.Balance,
		TotalReturn:     (bt.Balance - initialBalance) / initialBalance * 100,
		TradeCount:      bt.TradeCount,
		WinRate:         winRate,
		MaxDrawdown:     bt.MaxDrawdown * 100,
		StopLossCount:   bt.StopLossCount,
		TakeProfitCount: bt.TakeProfitCount,
		BestParams: types.TrendFollowConfigRequest{
			Symbol:               req.Symbol,
			EMAFast:              bestParams.EMAFast,
			EMASlow:              bestParams.EMASlow,
			InitialPositionRatio: bestParams.InitialPositionRatio,
			CallbackPercent:      bestParams.CallbackPercent,
			MaxAddPositions:      bestParams.MaxAddPositions,
			MaxPositionRatio:     bestParams.MaxPositionRatio,
			TakeProfitPercent:    bestParams.TakeProfitPercent,
			StopLossPercent:      bestParams.StopLossPercent,
			RSILowerBound:        bestParams.RSILowerBound,
			RSIUpperBound:        bestParams.RSIUpperBound,
			RSIExitBound:         bestParams.RSIExitBound,
			RSIPeriod:            bestParams.RSIPeriod,
			VolumeMultiplier:     bestParams.VolumeMultiplier,
			VolumePeriod:         bestParams.VolumePeriod,
			UpperShadowRatio:     bestParams.UpperShadowRatio,
		},
		Positions: positions,
	}, nil
}
