package main

import (
	"fmt"
	"time"

	"zblockchain/app/binance/logic"
	"zblockchain/common/routine"
)

func testRoutineOptimizer() {
	fmt.Println("=== 测试使用common/routine的马丁策略优化器 ===")

	// 创建模拟K线数据
	klines := createMockKlines()
	
	// 创建模拟资金费率数据
	fundingRates := createMockFundingRates()

	// 设置回测参数
	initialBalance := 10000.0
	feeRate := 0.04 / 100
	leverage := 10.0
	stopLossMultiplier := 1.5
	takeProfitMultiplier := 2.0
	maintenanceMarginRate := 0.005

	fmt.Printf("当前routine协程数: %d\n", routine.GetRoutineNumber())

	// 测试完整优化（使用routine并发）
	fmt.Println("\n--- 测试完整优化（使用routine并发） ---")
	start := time.Now()
	bestParams, bestBalance := logic.OptimizeMartingaleParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	duration := time.Since(start)
	
	fmt.Printf("完整优化耗时: %v\n", duration)
	fmt.Printf("最佳参数: 倍数=%.1f, 层数=%d, 止盈=%.1f%%, 止损=%.1f%%\n", 
		bestParams.Multiplier, bestParams.MaxLevels, 
		bestParams.TakeProfitPercent*100, bestParams.MaxLossPercent*100)
	fmt.Printf("最佳收益: %.2f USDT (%.2f%%)\n", bestBalance, (bestBalance-initialBalance)/initialBalance*100)
	fmt.Printf("优化后routine协程数: %d\n", routine.GetRoutineNumber())

	// 测试快速优化（顺序执行）
	fmt.Println("\n--- 测试快速优化（顺序执行） ---")
	start = time.Now()
	quickParams, quickBalance := logic.QuickOptimizeMartingaleParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	duration = time.Since(start)
	
	fmt.Printf("快速优化耗时: %v\n", duration)
	fmt.Printf("快速参数: 倍数=%.1f, 层数=%d, 止盈=%.1f%%, 止损=%.1f%%\n", 
		quickParams.Multiplier, quickParams.MaxLevels, 
		quickParams.TakeProfitPercent*100, quickParams.MaxLossPercent*100)
	fmt.Printf("快速收益: %.2f USDT (%.2f%%)\n", quickBalance, (quickBalance-initialBalance)/initialBalance*100)

	// 测试默认参数（无优化）
	fmt.Println("\n--- 测试默认参数（无优化） ---")
	start = time.Now()
	defaultParams := logic.GetDefaultMartingaleParams(initialBalance)
	
	// 运行回测
	bt := logic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt.Run(klines, fundingRates, func(klines []*logic.Kline, bt *logic.Backtest, index int) (logic.TradeSignal, float64) {
		return logic.MartingaleStrategy(klines, bt, index, defaultParams)
	})
	duration = time.Since(start)
	
	fmt.Printf("默认参数耗时: %v\n", duration)
	fmt.Printf("默认参数: 倍数=%.1f, 层数=%d, 止盈=%.1f%%, 止损=%.1f%%\n", 
		defaultParams.Multiplier, defaultParams.MaxLevels, 
		defaultParams.TakeProfitPercent*100, defaultParams.MaxLossPercent*100)
	fmt.Printf("默认收益: %.2f USDT (%.2f%%)\n", bt.Balance, (bt.Balance-initialBalance)/initialBalance*100)

	fmt.Printf("\n最终routine协程数: %d\n", routine.GetRoutineNumber())
	fmt.Println("=== 测试完成 ===")
}

// 创建模拟K线数据
func createMockKlines() []*logic.Kline {
	klines := make([]*logic.Kline, 100)
	basePrice := 50000.0
	timestamp := time.Now().Unix() * 1000

	for i := 0; i < 100; i++ {
		// 模拟价格波动
		priceChange := (float64(i%20) - 10) * 100 // 创建一些波动
		price := basePrice + priceChange
		
		klines[i] = &logic.Kline{
			Open:      price - 50,
			High:      price + 100,
			Low:       price - 100,
			Close:     price,
			Volume:    1000,
			Timestamp: timestamp + int64(i)*3600*1000, // 每小时一根K线
		}
	}
	
	return klines
}

// 创建模拟资金费率数据
func createMockFundingRates() []*logic.FundingRate {
	rates := make([]*logic.FundingRate, 10)
	timestamp := time.Now().Unix() * 1000

	for i := 0; i < 10; i++ {
		rates[i] = &logic.FundingRate{
			Symbol:    "BTCUSDT",
			Rate:      0.0001, // 0.01%
			Timestamp: timestamp + int64(i)*8*3600*1000, // 每8小时一次资金费率
		}
	}
	
	return rates
}

// func main() {
// 	testRoutineOptimizer()
// }
