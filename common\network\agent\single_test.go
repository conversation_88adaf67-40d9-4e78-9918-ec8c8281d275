package agent

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestSingleAgent(t *testing.T) {
	conn := new(mockConn)
	r := require.New(t)
	var a string
	conn.handle = func(b []byte) (n int, err error) {
		a = string(b)
		return len(a), errors.New("test error")
	}
	agent := GetSingleAgent()
	r.<PERSON>(agent)

	agent.OnConnect(conn)
	agent.OnMessage([]byte(""))
	agent.OnClose()
}
