package main

import (
	"context"
	"fmt"
	"log"
	"strings"

	"zblockchain/app/binance/internal/config"
	"zblockchain/app/binance/internal/logic"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/zeromicro/go-zero/core/conf"
)

func mainVerify() {
	// 加载配置
	var c config.Config
	conf.MustLoad("etc/binance.yaml", &c)

	fmt.Println("=== 币安配置验证 ===")
	fmt.Printf("服务名称: %s\n", c.Name)
	fmt.Printf("监听地址: %s:%d\n", c.Host, c.Port)
	fmt.Println()

	// 检查环境类型
	fmt.Println("=== 环境检查 ===")
	if strings.Contains(c.BaseUrl.Http, "testnet") {
		fmt.Println("✅ 当前环境: 测试网 (Testnet)")
		fmt.Println("✅ 安全性: 使用虚拟资金，无真实资金风险")

		// 检查期货测试网URL是否正确
		if strings.Contains(c.BaseUrl.Http, "binancefuture.com") {
			fmt.Println("✅ 期货API URL: 正确配置")
		} else {
			fmt.Println("⚠️  期货API URL: 将自动转换为 https://testnet.binancefuture.com")
		}
	} else if strings.Contains(c.BaseUrl.Http, "binance.com") {
		fmt.Println("⚠️  当前环境: 生产网 (Production)")
		fmt.Println("⚠️  警告: 使用真实资金，请谨慎操作")
	} else {
		fmt.Println("❌ 未知环境")
	}

	fmt.Printf("HTTP API: %s\n", c.BaseUrl.Http)
	fmt.Printf("WebSocket: %s\n", c.BaseUrl.Ws)
	fmt.Println()

	// 检查API密钥配置
	fmt.Println("=== API密钥检查 ===")
	if c.ApiKey != "" {
		fmt.Printf("✅ API Key: %s...%s (长度: %d)\n", 
			c.ApiKey[:8], c.ApiKey[len(c.ApiKey)-8:], len(c.ApiKey))
	} else {
		fmt.Println("❌ API Key 未配置")
	}

	if c.ApiSecret != "" {
		fmt.Printf("✅ API Secret: %s...%s (长度: %d)\n", 
			c.ApiSecret[:8], c.ApiSecret[len(c.ApiSecret)-8:], len(c.ApiSecret))
	} else {
		fmt.Println("❌ API Secret 未配置")
	}
	fmt.Println()

	// 测试API连接
	fmt.Println("=== API连接测试 ===")
	svcCtx := svc.NewServiceContext(c)
	ctx := context.Background()
	l := logic.NewGetKlinesLogic(ctx, svcCtx)

	// 测试获取BTCUSDT的1条K线数据
	req := &types.KlineRequest{
		Symbol:   "BTCUSDT",
		Interval: "1h",
		Limit:    1,
	}

	fmt.Printf("正在测试API连接 (%s %s)...\n", req.Symbol, req.Interval)
	resp, err := l.GetKlines(req)
	if err != nil {
		fmt.Printf("❌ API连接失败: %v\n", err)
		log.Printf("详细错误: %v", err)
	} else {
		fmt.Println("✅ API连接成功")
		if len(resp.Data) > 0 {
			kline := resp.Data[0]
			fmt.Printf("✅ 获取到K线数据: 开盘价=%.2f, 收盘价=%.2f\n", 
				kline.Open, kline.Close)
		}
	}
	fmt.Println()

	// 环境建议
	fmt.Println("=== 环境建议 ===")
	if strings.Contains(c.BaseUrl.Http, "testnet") {
		fmt.Println("✅ 推荐: 当前使用测试网，适合开发和测试")
		fmt.Println("📝 说明: 测试网使用虚拟资金，K线数据为真实市场数据")
		fmt.Println("🔄 切换到生产网: 修改BaseUrl为 https://fapi.binance.com")
	} else {
		fmt.Println("⚠️  警告: 当前使用生产网，请确保您了解风险")
		fmt.Println("🔄 切换到测试网: 修改BaseUrl为 https://testnet.binance.vision")
	}
}
