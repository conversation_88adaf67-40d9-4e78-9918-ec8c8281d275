package wallet

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"zblockchain/app/solana/internal/logic/wallet"
	"zblockchain/app/solana/internal/svc"
)

func BalanceHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := wallet.NewBalanceLogic(r.Context(), svcCtx)
		resp, err := l.Balance()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
