// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.2
// source: algorithm.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MathReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameId int64  `protobuf:"varint,1,opt,name=gameId,proto3" json:"gameId,omitempty"`
	Data   []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *MathReq) Reset() {
	*x = MathReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_algorithm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MathReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MathReq) ProtoMessage() {}

func (x *MathReq) ProtoReflect() protoreflect.Message {
	mi := &file_algorithm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MathReq.ProtoReflect.Descriptor instead.
func (*MathReq) Descriptor() ([]byte, []int) {
	return file_algorithm_proto_rawDescGZIP(), []int{0}
}

func (x *MathReq) GetGameId() int64 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *MathReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type MathResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  bool   `protobuf:"varint,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Data []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *MathResp) Reset() {
	*x = MathResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_algorithm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MathResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MathResp) ProtoMessage() {}

func (x *MathResp) ProtoReflect() protoreflect.Message {
	mi := &file_algorithm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MathResp.ProtoReflect.Descriptor instead.
func (*MathResp) Descriptor() ([]byte, []int) {
	return file_algorithm_proto_rawDescGZIP(), []int{1}
}

func (x *MathResp) GetRet() bool {
	if x != nil {
		return x.Ret
	}
	return false
}

func (x *MathResp) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_algorithm_proto protoreflect.FileDescriptor

var file_algorithm_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x02, 0x70, 0x62, 0x22, 0x35, 0x0a, 0x07, 0x4d, 0x61, 0x74, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x30, 0x0a, 0x08,
	0x4d, 0x61, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x31,
	0x0a, 0x09, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x24, 0x0a, 0x07, 0x4d,
	0x61, 0x74, 0x68, 0x52, 0x65, 0x73, 0x12, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x4d, 0x61, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x1a, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x4d, 0x61, 0x74, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_algorithm_proto_rawDescOnce sync.Once
	file_algorithm_proto_rawDescData = file_algorithm_proto_rawDesc
)

func file_algorithm_proto_rawDescGZIP() []byte {
	file_algorithm_proto_rawDescOnce.Do(func() {
		file_algorithm_proto_rawDescData = protoimpl.X.CompressGZIP(file_algorithm_proto_rawDescData)
	})
	return file_algorithm_proto_rawDescData
}

var file_algorithm_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_algorithm_proto_goTypes = []interface{}{
	(*MathReq)(nil),  // 0: pb.MathReq
	(*MathResp)(nil), // 1: pb.MathResp
}
var file_algorithm_proto_depIdxs = []int32{
	0, // 0: pb.Algorithm.MathRes:input_type -> pb.MathReq
	1, // 1: pb.Algorithm.MathRes:output_type -> pb.MathResp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algorithm_proto_init() }
func file_algorithm_proto_init() {
	if File_algorithm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_algorithm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MathReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_algorithm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MathResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_algorithm_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_algorithm_proto_goTypes,
		DependencyIndexes: file_algorithm_proto_depIdxs,
		MessageInfos:      file_algorithm_proto_msgTypes,
	}.Build()
	File_algorithm_proto = out.File
	file_algorithm_proto_rawDesc = nil
	file_algorithm_proto_goTypes = nil
	file_algorithm_proto_depIdxs = nil
}
