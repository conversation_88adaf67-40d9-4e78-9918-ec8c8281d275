{"swagger": "2.0", "info": {"title": "jinglefruits服务", "description": "jinglefruits服务", "version": ""}, "host": "**************:8886/", "basePath": "/", "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"jinglefruits/record": {"post": {"summary": "spin record", "operationId": "record", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/RecordResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RecordReq"}}], "requestBody": {}, "tags": ["spin"]}}, "jinglefruits/spin": {"post": {"summary": "spin", "operationId": "spin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SpinResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SpinReq"}}], "requestBody": {}, "tags": ["spin"]}}}, "definitions": {"Record": {"type": "object", "properties": {"bet": {"type": "number", "format": "double"}, "win": {"type": "number", "format": "double"}, "dateTime": {"type": "integer", "format": "int64"}}, "title": "Record", "required": ["bet", "win", "dateTime"]}, "RecordReq": {"type": "object", "title": "RecordReq"}, "RecordResp": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/Record"}}}, "title": "RecordResp", "required": ["records"]}, "Result": {"type": "object", "properties": {"index": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "title": "Result", "required": ["index"]}, "SpinReq": {"type": "object", "properties": {"spinMoney": {"type": "array", "items": {"$ref": "#/definitions/float64"}, "description": "下注金额 (0:bar 1:77 2:star 3:西瓜 4:铃铛  5：柠檬 6：橙子 7：苹果)"}}, "title": "SpinReq", "required": ["spinMoney"]}, "SpinResp": {"type": "object", "properties": {"resultIndexs": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "结果索引 （1~24）"}, "win": {"type": "number", "format": "double", "description": "奖励"}, "money": {"type": "number", "format": "double", "description": "余额"}}, "title": "SpinResp", "required": ["resultIndexs", "win", "money"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}, "security": [{"apiKey": []}]}