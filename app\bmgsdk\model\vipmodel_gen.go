// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vipFieldNames          = builder.RawFieldNames(&Vip{})
	vipRows                = strings.Join(vipFieldNames, ",")
	vipRowsExpectAutoSet   = strings.Join(stringx.Remove(vipFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	vipRowsWithPlaceHolder = strings.Join(stringx.Remove(vipFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	vipModel interface {
		Insert(ctx context.Context, data *Vip) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Vip, error)
		Update(ctx context.Context, data *Vip) error
		Delete(ctx context.Context, id int64) error
	}

	defaultVipModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Vip struct {
		Id         int64     `db:"id"`
		OpenId     string    `db:"openId"`
		VipId      int64     `db:"vipId"`
		ValidTime  int64     `db:"validTime"`
		UpdateTime time.Time `db:"updateTime"`
	}
)

func newVipModel(conn sqlx.SqlConn) *defaultVipModel {
	return &defaultVipModel{
		conn:  conn,
		table: "`vip`",
	}
}

func (m *defaultVipModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVipModel) FindOne(ctx context.Context, id int64) (*Vip, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vipRows, m.table)
	var resp Vip
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVipModel) Insert(ctx context.Context, data *Vip) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, vipRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OpenId, data.VipId, data.ValidTime, data.UpdateTime)
	return ret, err
}

func (m *defaultVipModel) Update(ctx context.Context, data *Vip) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vipRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.OpenId, data.VipId, data.ValidTime, data.UpdateTime, data.Id)
	return err
}

func (m *defaultVipModel) tableName() string {
	return m.table
}
