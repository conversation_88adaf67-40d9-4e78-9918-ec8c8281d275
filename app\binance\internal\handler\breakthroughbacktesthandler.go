package handler

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"zblockchain/app/binance/internal/logic"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"
)

func BreakthroughBacktestHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.BreakthroughBacktestRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewBreakthroughBacktestLogic(r.Context(), svcCtx)
		resp, err := l.BreakthroughBacktest(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
