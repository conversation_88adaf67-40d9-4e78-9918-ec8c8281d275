package handler

import (
	"net/http"

	"zblockchain/app/bmgsdk/internal/logic"
	"zblockchain/app/bmgsdk/internal/svc"
	"zblockchain/app/bmgsdk/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// login
func loginVerifyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.LoginReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewLoginVerifyLogic(r.Context(), svcCtx)
		resp, _ := l.LoginVerify(&req)
		httpx.OkJsonCtx(r.Context(), w, resp)
		// result.HttpResult(r, w, resp, err)
		// if err != nil {
		// 	httpx.ErrorCtx(r.Context(), w, err)
		// } else {
		// 	httpx.OkJsonCtx(r.Context(), w, resp)
		// }
	}
}
