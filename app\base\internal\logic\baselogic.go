package logic

import (
	"context"

	"zblockchain/app/base/internal/svc"
	"zblockchain/app/base/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BaseLogic) Base(req *types.Request) (resp *types.Response, err error) {
	// todo: add your logic here and delete this line

	return
}
