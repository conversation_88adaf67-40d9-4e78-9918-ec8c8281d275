package subscribe

import (
	"context"
	"zblockchain/app/solana/internal/svc"
	"zblockchain/common/routine"

	"github.com/zeromicro/go-zero/core/logx"
)

type RootSubscribe struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRootSubscribe(ctx context.Context, svcCtx *svc.ServiceContext) *RootSubscribe {
	return &RootSubscribe{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *RootSubscribe) AddRootSubscribe() error {
	return a.subscribe()
}

func (a *RootSubscribe) subscribe() error{
	err := routine.Run(true, func() {
		sub, err := a.svcCtx.QN_ws_clinet.RootSubscribe()

		if err != nil {
			panic(err)
		  }
		  defer sub.Unsubscribe()
	  
		  for {
			got, err := sub.Recv(a.ctx)
			if err != nil {
			  panic(err)
			}
			logx.Debugf("RootSubscribe got:%v", got)
		  }
	})
	return err
}
