package logic

import (
	"context"
	"time"

	"zblockchain/app/bmgsdk/internal/svc"
	"zblockchain/app/bmgsdk/internal/types"
	"zblockchain/app/bmgsdk/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type PayResultLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPayResultLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PayResultLogic {
	return &PayResultLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PayResultLogic) PayResult(req *types.PayResultReq) (*types.PayResultRsp, error) {
	// todo: add your logic here and delete this line
	logx.Infof("PayResult req:%v", req)
	resp := &types.PayResultRsp{
		Code: 1,
	}
	whereBuilder := l.svcCtx.PayModel.RowBuilder().Where(
		"openId = ? And orderId = ? And status=0",
		req.OpenId,
		req.OrderId,
	)
	pay, err := l.svcCtx.PayModel.FindOneByQuery(l.ctx, whereBuilder)
	if err != nil || pay == nil {
		resp.Code = 2
		logx.Errorf("PayResult db err:%v,%v", pay, err)
	}

	if pay != nil {
		pay.Status = 1
		_ = l.svcCtx.PayModel.Update(l.ctx, pay)

		vipId := 0
		if pay.ProductId == "vip_1" {
			vipId = 1
		} else if pay.ProductId == "vip_2" {
			vipId = 2
		}
		whereBuilder = l.svcCtx.VipModel.RowBuilder().Where(
			"openId = ?",
			req.OpenId,
		)

		vipArry, err := l.svcCtx.VipModel.FindByQuery(l.ctx, whereBuilder)
		if err != nil {
			resp.Code = 2
			logx.Errorf("vipResult db err:%v,%v", vipArry, err)
		} else {
			nowN := time.Now()
			needInsert := true
			for _, vip := range vipArry {
				if vip.VipId == int64(vipId) {
					needInsert = false
				}
				if vip.ValidTime > 0 {
					duration := nowN.Sub(vip.UpdateTime).Seconds()
					vip.ValidTime = vip.ValidTime - int64(duration)
					if vip.ValidTime < 0 {
						vip.ValidTime = 0
					}
					if pay.ProductId == "vip_1" && vip.VipId == 1 {
						vip.ValidTime += 86400 * 30
					}
					if err := l.svcCtx.VipModel.Update(l.ctx, vip); err != nil {
						logx.Errorf("update vip faild! value:%v, err:%v", vip, err)
					}
				}
				rVip := &types.Vip{
					Id:   vip.VipId,
					Time: vip.ValidTime,
				}
				resp.VipInfo = append(resp.VipInfo, rVip)
			}
			if needInsert {
				vipDb := &model.Vip{}
				vipDb.VipId = int64(vipId)
				vipDb.OpenId = req.OpenId
				vipDb.UpdateTime = time.Now()
				if vipId == 1 {
					vipDb.ValidTime = 86400 * 30
				} else {
					vipDb.ValidTime = -1
				}
				if _, err := l.svcCtx.VipModel.Insert(l.ctx, vipDb); err != nil {
					logx.Errorf("insert vip faild! value:%v, err:%v", vipDb, err)
				}
				rVip := &types.Vip{
					Id:   int64(vipId),
					Time: vipDb.ValidTime,
				}
				resp.VipInfo = append(resp.VipInfo, rVip)
			}

		}

	}
	logx.Infof("PayResult resp:%v", resp)
	return resp, nil
}
