package logic

import (
	"context"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BreakthroughConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBreakthroughConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BreakthroughConfigLogic {
	return &BreakthroughConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BreakthroughConfigLogic) BreakthroughConfig(req *types.BreakthroughConfigRequest) (resp *types.BreakthroughConfigResponse, err error) {
	// 验证双向突破策略参数
	if req.EMAFast <= 0 || req.EMASlow <= 0 || req.EMAFast >= req.EMASlow {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "EMA参数错误：快线周期必须小于慢线周期且都大于0",
		}, nil
	}

	if req.BollingerPeriod <= 0 || req.BollingerPeriod < req.EMASlow {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "布林带周期必须大于0且不小于EMA慢线周期",
		}, nil
	}

	if req.BollingerStdDev <= 0 || req.BollingerStdDev > 3 {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "布林带标准差必须在0-3之间",
		}, nil
	}

	if req.BreakoutThreshold <= 0 || req.BreakoutThreshold > 0.02 {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "突破阈值必须在0-2%之间",
		}, nil
	}

	if req.TakeProfitMin <= 0 || req.TakeProfitMax <= req.TakeProfitMin || req.TakeProfitMax > 0.05 {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "止盈参数错误：最小值必须小于最大值，且最大值不超过5%",
		}, nil
	}

	if req.StopLossMin <= 0 || req.StopLossMax <= req.StopLossMin || req.StopLossMax > 0.02 {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "止损参数错误：最小值必须小于最大值，且最大值不超过2%",
		}, nil
	}

	if req.PositionSize <= 0 || req.PositionSize > req.InitialBalance {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "仓位大小必须大于0且不超过初始资金",
		}, nil
	}

	if req.LeverageDefault <= 0 || req.LeverageMax <= req.LeverageDefault || req.LeverageMax > 100 {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "杠杆参数错误：默认杠杆必须小于最大杠杆，且最大杠杆不超过100倍",
		}, nil
	}

	if req.MaxPositions <= 0 || req.MaxPositions > 10 {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "最大持仓数必须在1-10之间",
		}, nil
	}

	if req.DailyTarget <= 0 || req.DailyTarget > req.InitialBalance {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "每日目标必须大于0且不超过初始资金",
		}, nil
	}

	if req.MaxDrawdown <= 0 || req.MaxDrawdown > req.InitialBalance {
		return &types.BreakthroughConfigResponse{
			Success: false,
			Message: "最大回撤必须大于0且不超过初始资金",
		}, nil
	}

	// TODO: 这里可以将配置保存到数据库或Redis
	// 目前只是简单返回成功

	return &types.BreakthroughConfigResponse{
		Success: true,
		Message: "双向突破策略配置保存成功",
	}, nil
}
