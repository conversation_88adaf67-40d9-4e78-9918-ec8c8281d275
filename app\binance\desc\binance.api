syntax = "v1"

type PriceRequest {
	Symbol string `path:"symbol"`
}

type OrderRequest {
	Symbol   string  `json:"symbol"`
	Side     string  `json:"side"` // BUY or SELL
	Type     string  `json:"type"` // LIMIT, MARKET, etc.
	Quantity float64 `json:"quantity"`
	Price    float64 `json:"price"`
}

type PriceResponse {
	Symbol string  `json:"symbol"`
	Price  float64 `json:"price"`
}

type OrderResponse {
	OrderId int64  `json:"orderId"`
	Symbol  string `json:"symbol"`
	Status  string `json:"status"`
}

// 健康检查
type HealthResponse {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
	Version   string `json:"version"`
}

// 账户信息
type AccountInfo {
	TotalWalletBalance          float64        `json:"totalWalletBalance"`
	TotalUnrealizedProfit       float64        `json:"totalUnrealizedProfit"`
	TotalMarginBalance          float64        `json:"totalMarginBalance"`
	TotalPositionInitialMargin  float64        `json:"totalPositionInitialMargin"`
	TotalOpenOrderInitialMargin float64        `json:"totalOpenOrderInitialMargin"`
	AvailableBalance            float64        `json:"availableBalance"`
	MaxWithdrawAmount           float64        `json:"maxWithdrawAmount"`
	Assets                      []AssetBalance `json:"assets"`
	Positions                   []PositionInfo `json:"positions"`
}

type AssetBalance {
	Asset                  string  `json:"asset"`
	WalletBalance          float64 `json:"walletBalance"`
	UnrealizedProfit       float64 `json:"unrealizedProfit"`
	MarginBalance          float64 `json:"marginBalance"`
	MaintMargin            float64 `json:"maintMargin"`
	InitialMargin          float64 `json:"initialMargin"`
	PositionInitialMargin  float64 `json:"positionInitialMargin"`
	OpenOrderInitialMargin float64 `json:"openOrderInitialMargin"`
	MaxWithdrawAmount      float64 `json:"maxWithdrawAmount"`
	CrossWalletBalance     float64 `json:"crossWalletBalance"`
	CrossUnPnl             float64 `json:"crossUnPnl"`
	AvailableBalance       float64 `json:"availableBalance"`
}

// 持仓信息
type PositionInfo {
	Symbol           string  `json:"symbol"`
	PositionAmt      float64 `json:"positionAmt"`
	EntryPrice       float64 `json:"entryPrice"`
	MarkPrice        float64 `json:"markPrice"`
	UnRealizedProfit float64 `json:"unRealizedProfit"`
	LiquidationPrice float64 `json:"liquidationPrice"`
	Leverage         float64 `json:"leverage"`
	MaxNotionalValue float64 `json:"maxNotionalValue"`
	MarginType       string  `json:"marginType"`
	IsolatedMargin   float64 `json:"isolatedMargin"`
	IsAutoAddMargin  bool    `json:"isAutoAddMargin"`
	PositionSide     string  `json:"positionSide"`
	Notional         float64 `json:"notional"`
	IsolatedWallet   float64 `json:"isolatedWallet"`
	UpdateTime       int64   `json:"updateTime"`
}

// 历史订单请求
type OrderHistoryRequest {
	Symbol    string `form:"symbol"`
	OrderId   int64  `form:"orderId,optional"`
	StartTime int64  `form:"startTime,optional"`
	EndTime   int64  `form:"endTime,optional"`
	Limit     int    `form:"limit,optional"`
}

// 当前挂单请求
type OpenOrdersRequest {
	Symbol string `form:"symbol,optional"`
}

// 订单列表响应
type OrderListResponse {
	Orders []OrderResponse `json:"orders"`
}

// K线数据
type KlineRequest {
	Symbol    string `form:"symbol"`
	Interval  string `form:"interval"`
	StartTime int64  `form:"startTime,optional"`
	EndTime   int64  `form:"endTime,optional"`
	Limit     int    `form:"limit,optional"`
}

type KlineData {
	OpenTime  int64   `json:"openTime"`
	Open      float64 `json:"open"`
	High      float64 `json:"high"`
	Low       float64 `json:"low"`
	Close     float64 `json:"close"`
	Volume    float64 `json:"volume"`
	CloseTime int64   `json:"closeTime"`
}

type KlineResponse {
	Symbol string      `json:"symbol"`
	Data   []KlineData `json:"data"`
}

type MartingaleConfigRequest {
	Symbol              string  `json:"symbol"`
	InitialPositionSize float64 `json:"initialPositionSize"`
	Multiplier          float64 `json:"multiplier"`
	MaxLevels           int     `json:"maxLevels"`
	TakeProfitPercent   float64 `json:"takeProfitPercent"`
	MaxLossPercent      float64 `json:"maxLossPercent"`
	GridSpacing         float64 `json:"gridSpacing"`
	UseATRSpacing       bool    `json:"useATRSpacing"`
	ATRPeriod           int     `json:"atrPeriod"`
	ATRMultiplier       float64 `json:"atrMultiplier"`
}

type MartingaleConfigResponse {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type MartingaleBacktestRequest {
	Symbol        string `json:"symbol"`
	Interval      string `json:"interval"`
	StartTime     int64  `json:"startTime"`
	EndTime       int64  `json:"endTime"`
	UseOptimize   bool   `json:"useOptimize"`
	QuickOptimize bool   `json:"quickOptimize"` // 快速优化，仅测试3个预设策略
}

type MartingaleLevel {
	Level      int     `json:"level"`
	EntryPrice float64 `json:"entryPrice"`
	Size       float64 `json:"size"`
	Timestamp  int64   `json:"timestamp"`
}

type MartingaleBacktestResponse {
	Success          bool                    `json:"success"`
	Message          string                  `json:"message"`
	InitialBalance   float64                 `json:"initialBalance"`
	FinalBalance     float64                 `json:"finalBalance"`
	TotalReturn      float64                 `json:"totalReturn"`
	TradeCount       int                     `json:"tradeCount"`
	WinRate          float64                 `json:"winRate"`
	MaxDrawdown      float64                 `json:"maxDrawdown"`
	StopLossCount    int                     `json:"stopLossCount"`
	TakeProfitCount  int                     `json:"takeProfitCount"`
	LiquidationCount int                     `json:"liquidationCount"`
	BestParams       MartingaleConfigRequest `json:"bestParams"`
	Levels           []MartingaleLevel       `json:"levels"`
}

type MartingaleStatusResponse {
	IsRunning       bool              `json:"isRunning"`
	CurrentLevels   []MartingaleLevel `json:"currentLevels"`
	TotalPnL        float64           `json:"totalPnL"`
	TotalInvestment float64           `json:"totalInvestment"`
	CurrentPrice    float64           `json:"currentPrice"`
}

type TrendFollowConfigRequest {
	Symbol               string  `json:"symbol"`
	EMAFast              int     `json:"emaFast"`
	EMASlow              int     `json:"emaSlow"`
	InitialPositionRatio float64 `json:"initialPositionRatio"`
	CallbackPercent      float64 `json:"callbackPercent"`
	MaxAddPositions      int     `json:"maxAddPositions"`
	MaxPositionRatio     float64 `json:"maxPositionRatio"`
	TakeProfitPercent    float64 `json:"takeProfitPercent"`
	StopLossPercent      float64 `json:"stopLossPercent"`
	RSILowerBound        float64 `json:"rsiLowerBound"`
	RSIUpperBound        float64 `json:"rsiUpperBound"`
	RSIExitBound         float64 `json:"rsiExitBound"`
	RSIPeriod            int     `json:"rsiPeriod"`
	VolumeMultiplier     float64 `json:"volumeMultiplier"`
	VolumePeriod         int     `json:"volumePeriod"`
	UpperShadowRatio     float64 `json:"upperShadowRatio"`
}

type TrendFollowConfigResponse {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type TrendFollowBacktestRequest {
	Symbol      string `json:"symbol"`
	Interval    string `json:"interval"`
	StartTime   int64  `json:"startTime"`
	EndTime     int64  `json:"endTime"`
	UseOptimize bool   `json:"useOptimize"`
}

type TrendFollowPosition {
	Level      int     `json:"level"`
	EntryPrice float64 `json:"entryPrice"`
	Size       float64 `json:"size"`
	Timestamp  int64   `json:"timestamp"`
	IsActive   bool    `json:"isActive"`
}

type TrendFollowBacktestResponse {
	Success         bool                     `json:"success"`
	Message         string                   `json:"message"`
	InitialBalance  float64                  `json:"initialBalance"`
	FinalBalance    float64                  `json:"finalBalance"`
	TotalReturn     float64                  `json:"totalReturn"`
	TradeCount      int                      `json:"tradeCount"`
	WinRate         float64                  `json:"winRate"`
	MaxDrawdown     float64                  `json:"maxDrawdown"`
	StopLossCount   int                      `json:"stopLossCount"`
	TakeProfitCount int                      `json:"takeProfitCount"`
	BestParams      TrendFollowConfigRequest `json:"bestParams"`
	Positions       []TrendFollowPosition    `json:"positions"`
}

type TrendFollowStatusResponse {
	IsRunning        bool                  `json:"isRunning"`
	CurrentPositions []TrendFollowPosition `json:"currentPositions"`
	TotalPnL         float64               `json:"totalPnL"`
	TotalInvestment  float64               `json:"totalInvestment"`
	CurrentPrice     float64               `json:"currentPrice"`
	AvgEntryPrice    float64               `json:"avgEntryPrice"`
}

type BreakthroughConfigRequest {
	EMAFast           int     `json:"emaFast"`
	EMASlow           int     `json:"emaSlow"`
	BollingerPeriod   int     `json:"bollingerPeriod"`
	BollingerStdDev   float64 `json:"bollingerStdDev"`
	BreakoutThreshold float64 `json:"breakoutThreshold"`
	TakeProfitMin     float64 `json:"takeProfitMin"`
	TakeProfitMax     float64 `json:"takeProfitMax"`
	StopLossMin       float64 `json:"stopLossMin"`
	StopLossMax       float64 `json:"stopLossMax"`
	PositionSize      float64 `json:"positionSize"`
	LeverageDefault   float64 `json:"leverageDefault"`
	LeverageMax       float64 `json:"leverageMax"`
	MaxPositions      int     `json:"maxPositions"`
	DailyTarget       float64 `json:"dailyTarget"`
	MaxDrawdown       float64 `json:"maxDrawdown"`
	VolumeMultiplier  float64 `json:"volumeMultiplier"`
	VolumePeriod      int     `json:"volumePeriod"`
	MomentumPeriod    int     `json:"momentumPeriod"`
	InitialBalance    float64 `json:"initialBalance"`
}

type BreakthroughConfigResponse {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type BreakthroughBacktestRequest {
	Symbols     []string `json:"symbols"`
	Interval    string   `json:"interval"`
	StartTime   int64    `json:"startTime"`
	EndTime     int64    `json:"endTime"`
	UseOptimize bool     `json:"useOptimize"`
}

type BreakthroughPosition {
	ID         int     `json:"id"`
	Symbol     string  `json:"symbol"`
	Direction  string  `json:"direction"`
	EntryPrice float64 `json:"entryPrice"`
	Size       float64 `json:"size"`
	Leverage   float64 `json:"leverage"`
	TakeProfit float64 `json:"takeProfit"`
	StopLoss   float64 `json:"stopLoss"`
	Timestamp  int64   `json:"timestamp"`
	IsActive   bool    `json:"isActive"`
}

type BreakthroughBacktestResponse {
	Success         bool                      `json:"success"`
	Message         string                    `json:"message"`
	InitialBalance  float64                   `json:"initialBalance"`
	FinalBalance    float64                   `json:"finalBalance"`
	TotalReturn     float64                   `json:"totalReturn"`
	TradeCount      int                       `json:"tradeCount"`
	WinRate         float64                   `json:"winRate"`
	MaxDrawdown     float64                   `json:"maxDrawdown"`
	DailyPnL        float64                   `json:"dailyPnL"`
	TotalPnL        float64                   `json:"totalPnL"`
	BestParams      BreakthroughConfigRequest `json:"bestParams"`
	Positions       []BreakthroughPosition    `json:"positions"`
	ActivePositions int                       `json:"activePositions"`
}

type BreakthroughStatusResponse {
	IsRunning        bool                   `json:"isRunning"`
	CurrentPositions []BreakthroughPosition `json:"currentPositions"`
	DailyPnL         float64                `json:"dailyPnL"`
	TotalPnL         float64                `json:"totalPnL"`
	DailyTarget      float64                `json:"dailyTarget"`
	MaxDrawdown      float64                `json:"maxDrawdown"`
	ActivePositions  int                    `json:"activePositions"`
	CurrentBalance   float64                `json:"currentBalance"`
}

service binance {
	@handler HealthCheck
	get /health returns (HealthResponse)

	@handler GetKlines
	get /klines (KlineRequest) returns (KlineResponse)

	@handler GetPrice
	get /price/:symbol (PriceRequest) returns (PriceResponse)

	@handler CreateOrder
	post /order (OrderRequest) returns (OrderResponse)

	@handler GetAccount
	get /account returns (AccountInfo)

	@handler GetPositions
	get /positions returns ([]PositionInfo)

	@handler GetOrderHistory
	get /orders/history (OrderHistoryRequest) returns (OrderListResponse)

	@handler GetOpenOrders
	get /orders/open (OpenOrdersRequest) returns (OrderListResponse)

	@handler MartingaleBacktest
	post /martingale/backtest (MartingaleBacktestRequest) returns (MartingaleBacktestResponse)

	@handler MartingaleConfig
	post /martingale/config (MartingaleConfigRequest) returns (MartingaleConfigResponse)

	@handler MartingaleStatus
	get /martingale/status returns (MartingaleStatusResponse)

	@handler TrendFollowBacktest
	post /trendfollow/backtest (TrendFollowBacktestRequest) returns (TrendFollowBacktestResponse)

	@handler TrendFollowConfig
	post /trendfollow/config (TrendFollowConfigRequest) returns (TrendFollowConfigResponse)

	@handler TrendFollowStatus
	get /trendfollow/status returns (TrendFollowStatusResponse)

	@handler BreakthroughBacktest
	post /breakthrough/backtest (BreakthroughBacktestRequest) returns (BreakthroughBacktestResponse)

	@handler BreakthroughConfig
	post /breakthrough/config (BreakthroughConfigRequest) returns (BreakthroughConfigResponse)

	@handler BreakthroughStatus
	get /breakthrough/status returns (BreakthroughStatusResponse)
}

