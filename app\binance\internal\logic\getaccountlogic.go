package logic

import (
	"context"
	"fmt"
	"strconv"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetAccountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAccountLogic {
	return &GetAccountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAccountLogic) GetAccount() (resp *types.AccountInfo, err error) {
	// 创建币安期货客户端
	client := futures.NewClient(l.svcCtx.Config.ApiKey, l.svcCtx.Config.ApiSecret)
	client.BaseURL = l.svcCtx.Config.BaseUrl.Http

	// 获取账户信息
	account, err := client.NewGetAccountService().Do(l.ctx)
	if err != nil {
		l.Errorf("获取账户信息失败: %v", err)
		return nil, fmt.Errorf("获取账户信息失败: %v", err)
	}

	// 转换资产信息
	assets := make([]types.AssetBalance, len(account.Assets))
	for i, asset := range account.Assets {
		walletBalance, _ := strconv.ParseFloat(asset.WalletBalance, 64)
		unrealizedProfit, _ := strconv.ParseFloat(asset.UnrealizedProfit, 64)
		marginBalance, _ := strconv.ParseFloat(asset.MarginBalance, 64)
		maintMargin, _ := strconv.ParseFloat(asset.MaintMargin, 64)
		initialMargin, _ := strconv.ParseFloat(asset.InitialMargin, 64)
		positionInitialMargin, _ := strconv.ParseFloat(asset.PositionInitialMargin, 64)
		openOrderInitialMargin, _ := strconv.ParseFloat(asset.OpenOrderInitialMargin, 64)
		maxWithdrawAmount, _ := strconv.ParseFloat(asset.MaxWithdrawAmount, 64)
		crossWalletBalance, _ := strconv.ParseFloat(asset.CrossWalletBalance, 64)
		crossUnPnl, _ := strconv.ParseFloat(asset.CrossUnPnl, 64)
		availableBalance, _ := strconv.ParseFloat(asset.AvailableBalance, 64)

		assets[i] = types.AssetBalance{
			Asset:                  asset.Asset,
			WalletBalance:          walletBalance,
			UnrealizedProfit:       unrealizedProfit,
			MarginBalance:          marginBalance,
			MaintMargin:            maintMargin,
			InitialMargin:          initialMargin,
			PositionInitialMargin:  positionInitialMargin,
			OpenOrderInitialMargin: openOrderInitialMargin,
			MaxWithdrawAmount:      maxWithdrawAmount,
			CrossWalletBalance:     crossWalletBalance,
			CrossUnPnl:             crossUnPnl,
			AvailableBalance:       availableBalance,
		}
	}

	// 转换持仓信息
	positions := make([]types.PositionInfo, len(account.Positions))
	for i, pos := range account.Positions {
		positionAmt, _ := strconv.ParseFloat(pos.PositionAmt, 64)
		entryPrice, _ := strconv.ParseFloat(pos.EntryPrice, 64)
		unRealizedProfit, _ := strconv.ParseFloat(pos.UnrealizedProfit, 64)
		leverage, _ := strconv.ParseFloat(pos.Leverage, 64)

		positions[i] = types.PositionInfo{
			Symbol:           pos.Symbol,
			PositionAmt:      positionAmt,
			EntryPrice:       entryPrice,
			MarkPrice:        0, // AccountPosition doesn't have MarkPrice
			UnRealizedProfit: unRealizedProfit,
			LiquidationPrice: 0, // AccountPosition doesn't have LiquidationPrice
			Leverage:         leverage,
			MaxNotionalValue: 0, // AccountPosition doesn't have MaxNotionalValue
			MarginType:       "", // AccountPosition doesn't have MarginType
			IsolatedMargin:   0, // AccountPosition doesn't have IsolatedMargin
			IsAutoAddMargin:  false, // AccountPosition doesn't have IsAutoAddMargin
			PositionSide:     string(pos.PositionSide),
			Notional:         0, // AccountPosition doesn't have Notional
			IsolatedWallet:   0, // AccountPosition doesn't have IsolatedWallet
			UpdateTime:       pos.UpdateTime,
		}
	}

	// 解析总体账户信息
	totalWalletBalance, _ := strconv.ParseFloat(account.TotalWalletBalance, 64)
	totalUnrealizedProfit, _ := strconv.ParseFloat(account.TotalUnrealizedProfit, 64)
	totalMarginBalance, _ := strconv.ParseFloat(account.TotalMarginBalance, 64)
	totalPositionInitialMargin, _ := strconv.ParseFloat(account.TotalPositionInitialMargin, 64)
	totalOpenOrderInitialMargin, _ := strconv.ParseFloat(account.TotalOpenOrderInitialMargin, 64)
	availableBalance, _ := strconv.ParseFloat(account.AvailableBalance, 64)
	maxWithdrawAmount, _ := strconv.ParseFloat(account.MaxWithdrawAmount, 64)

	return &types.AccountInfo{
		TotalWalletBalance:          totalWalletBalance,
		TotalUnrealizedProfit:       totalUnrealizedProfit,
		TotalMarginBalance:          totalMarginBalance,
		TotalPositionInitialMargin:  totalPositionInitialMargin,
		TotalOpenOrderInitialMargin: totalOpenOrderInitialMargin,
		AvailableBalance:            availableBalance,
		MaxWithdrawAmount:           maxWithdrawAmount,
		Assets:                      assets,
		Positions:                   positions,
	}, nil
}
