// Code generated by goctl. DO NOT EDIT.
// Source: algorithm.proto

package algorithm

import (
	"context"

	"zblockchain/app/algorithm/pb"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	MathReq  = pb.MathReq
	MathResp = pb.MathResp

	Algorithm interface {
		MathRes(ctx context.Context, in *MathReq, opts ...grpc.CallOption) (*MathResp, error)
	}

	defaultAlgorithm struct {
		cli zrpc.Client
	}
)

func NewAlgorithm(cli zrpc.Client) Algorithm {
	return &defaultAlgorithm{
		cli: cli,
	}
}

func (m *defaultAlgorithm) MathRes(ctx context.Context, in *MathReq, opts ...grpc.CallOption) (*MathResp, error) {
	client := pb.NewAlgorithmClient(m.cli.Conn())
	return client.MathRes(ctx, in, opts...)
}
