package logic

import (
	"math"

	"github.com/markcheno/go-talib"
)

type TradeSignal int

const (
	NoSignal TradeSignal = iota
	BuyLong
	SellLong
	BuyShort
	SellShort
)

type RSIParams struct {
	Period               int     // RSI 周期
	BuyThreshold         float64 // 买入阈值（超卖）
	SellThreshold        float64 // 卖出阈值（超买）
	StopLossMultiplier   float64 // ATR 止损倍数
	TakeProfitMultiplier float64 // ATR 止盈倍数
}

// MartingaleParams 马丁策略参数
type MartingaleParams struct {
	InitialPositionSize  float64 // 初始仓位大小（USDT）
	Multiplier           float64 // 加仓倍数（默认2.0）
	MaxLevels            int     // 最大加仓层数
	TakeProfitPercent    float64 // 止盈百分比
	MaxLossPercent       float64 // 最大亏损百分比（风险控制）
	GridSpacing          float64 // 网格间距百分比
	UseATRSpacing        bool    // 是否使用ATR动态间距
	ATRPeriod            int     // ATR周期
	ATRMultiplier        float64 // ATR倍数
}

func RSIStrategy(klines []*Kline, bt *Backtest, index int, params RSIParams) (TradeSignal, float64) {
	if index < params.Period {
		return NoSignal, 0
	}

	// 提取价格数据
	closes := make([]float64, index+1)
	highs := make([]float64, index+1)
	lows := make([]float64, index+1)
	for i := 0; i <= index; i++ {
		closes[i] = klines[i].Close
		highs[i] = klines[i].High
		lows[i] = klines[i].Low
	}

	// 计算 RSI
	rsi := talib.Rsi(closes, params.Period)
	currentRsi := rsi[len(rsi)-1]

	// 计算 ATR
	atr := talib.Atr(highs, lows, closes, params.Period)
	currentAtr := atr[len(atr)-1]

	// 设置动态止损和止盈价格
	if bt.Position == 0 {
		if bt.NextStopLossPrice == 0 && bt.NextTakeProfitPrice == 0 {
			if currentRsi < params.BuyThreshold {
				// 做多：止损 = 当前价 - ATR × 倍数，止盈 = 当前价 + ATR × 倍数
				bt.NextStopLossPrice = klines[index].Close - currentAtr*params.StopLossMultiplier
				bt.NextTakeProfitPrice = klines[index].Close + currentAtr*params.TakeProfitMultiplier
				return BuyLong, klines[index].Close
			} else if currentRsi > params.SellThreshold {
				// 做空：止损 = 当前价 + ATR × 倍数，止盈 = 当前价 - ATR × 倍数
				bt.NextStopLossPrice = klines[index].Close + currentAtr*params.StopLossMultiplier
				bt.NextTakeProfitPrice = klines[index].Close - currentAtr*params.TakeProfitMultiplier
				return BuyShort, klines[index].Close
			}
		}
	} else if bt.Position > 0 && currentRsi > params.SellThreshold {
		return SellLong, klines[index].Close
	} else if bt.Position < 0 && currentRsi < params.BuyThreshold {
		return SellShort, klines[index].Close
	}

	return NoSignal, 0
}

// MartingaleStrategy 马丁策略实现
func MartingaleStrategy(klines []*Kline, bt *Backtest, index int, params MartingaleParams) (TradeSignal, float64) {
	if index < 1 {
		return NoSignal, 0
	}

	currentPrice := klines[index].Close

	// 设置马丁策略标志
	bt.IsMartingaleStrategy = true

	// 如果没有仓位，开始第一层
	if len(bt.MartingaleLevels) == 0 {
		// 开始第一层做多
		return BuyLong, currentPrice
	}

	// 检查是否需要加仓
	lastLevel := bt.MartingaleLevels[len(bt.MartingaleLevels)-1]

	// 计算当前总盈亏
	totalPnL := bt.calculateMartingalePnL(currentPrice)
	totalInvestment := bt.calculateTotalInvestment()

	// 风险控制：检查最大亏损
	if totalPnL < 0 && totalInvestment > 0 && math.Abs(totalPnL)/totalInvestment > params.MaxLossPercent {
		// 强制平仓
		return SellLong, currentPrice
	}

	// 检查止盈
	if totalPnL > 0 && totalInvestment > 0 && totalPnL/totalInvestment > params.TakeProfitPercent {
		return SellLong, currentPrice
	}

	// 检查是否需要加仓（价格下跌到加仓点）
	if len(bt.MartingaleLevels) < params.MaxLevels {
		spacing := params.GridSpacing
		if params.UseATRSpacing && index >= params.ATRPeriod {
			closes := make([]float64, index+1)
			highs := make([]float64, index+1)
			lows := make([]float64, index+1)
			for i := 0; i <= index; i++ {
				closes[i] = klines[i].Close
				highs[i] = klines[i].High
				lows[i] = klines[i].Low
			}
			atr := talib.Atr(highs, lows, closes, params.ATRPeriod)
			if len(atr) > 0 {
				spacing = (atr[len(atr)-1] / currentPrice) * params.ATRMultiplier
			}
		}

		triggerPrice := lastLevel.EntryPrice * (1 - spacing)
		if currentPrice <= triggerPrice {
			// 加仓
			return BuyLong, currentPrice
		}
	}

	return NoSignal, 0
}

// TrendFollowParams 趋势跟随+回调加仓策略参数
type TrendFollowParams struct {
	EMAFast              int     // EMA快线周期（默认5）
	EMASlow              int     // EMA慢线周期（默认20）
	InitialPositionRatio float64 // 初始仓位比例（默认20%）
	CallbackPercent      float64 // 回调加仓触发百分比（默认2%）
	MaxAddPositions      int     // 最大加仓次数（默认2次）
	MaxPositionRatio     float64 // 最大持仓比例（默认60%）
	TakeProfitPercent    float64 // 止盈百分比（默认6%）
	StopLossPercent      float64 // 止损百分比（默认1.5%）
	RSILowerBound        float64 // RSI下界（默认40）
	RSIUpperBound        float64 // RSI上界（默认70）
	RSIExitBound         float64 // RSI出场界限（默认80）
	RSIPeriod            int     // RSI周期（默认14）
	VolumeMultiplier     float64 // 成交量倍数（默认1.2）
	VolumePeriod         int     // 成交量平均周期（默认5）
	UpperShadowRatio     float64 // 上影线比例阈值（默认0.3）
}

// TrendFollowPosition 趋势跟随策略持仓信息
type TrendFollowPosition struct {
	Level       int     // 持仓层级（1,2,3）
	EntryPrice  float64 // 入场价格
	Size        float64 // 仓位大小（USDT）
	Timestamp   int64   // 入场时间
	IsActive    bool    // 是否活跃
}

// GetDefaultTrendFollowParams 获取默认趋势跟随策略参数
func GetDefaultTrendFollowParams(initialBalance float64) TrendFollowParams {
	return TrendFollowParams{
		EMAFast:              5,     // EMA快线周期
		EMASlow:              20,    // EMA慢线周期
		InitialPositionRatio: 0.2,   // 初始仓位比例20%
		CallbackPercent:      0.02,  // 回调2%加仓
		MaxAddPositions:      2,     // 最多加仓2次
		MaxPositionRatio:     0.6,   // 最大持仓60%
		TakeProfitPercent:    0.06,  // 止盈6%
		StopLossPercent:      0.015, // 止损1.5%
		RSILowerBound:        40,    // RSI下界40
		RSIUpperBound:        70,    // RSI上界70
		RSIExitBound:         80,    // RSI出场界限80
		RSIPeriod:            14,    // RSI周期14
		VolumeMultiplier:     1.2,   // 成交量倍数1.2
		VolumePeriod:         5,     // 成交量平均周期5
		UpperShadowRatio:     0.3,   // 上影线比例阈值30%
	}
}

// TrendFollowStrategy 趋势跟随+回调加仓策略实现
func TrendFollowStrategy(klines []*Kline, bt *Backtest, index int, params TrendFollowParams) (TradeSignal, float64) {
	if index < params.EMASlow {
		return NoSignal, 0
	}

	currentPrice := klines[index].Close

	// 设置趋势跟随策略标志
	bt.IsTrendFollowStrategy = true

	// 计算EMA指标
	closes := make([]float64, index+1)
	for i := 0; i <= index; i++ {
		closes[i] = klines[i].Close
	}

	emaFast := talib.Ema(closes, params.EMAFast)
	emaSlow := talib.Ema(closes, params.EMASlow)

	if len(emaFast) == 0 || len(emaSlow) == 0 {
		return NoSignal, 0
	}

	currentEMAFast := emaFast[len(emaFast)-1]
	currentEMASlow := emaSlow[len(emaSlow)-1]

	// 计算RSI指标
	rsi := talib.Rsi(closes, params.RSIPeriod)
	if len(rsi) == 0 {
		return NoSignal, 0
	}
	currentRSI := rsi[len(rsi)-1]

	// 计算成交量指标
	volumes := make([]float64, index+1)
	for i := 0; i <= index; i++ {
		volumes[i] = klines[i].Volume
	}
	avgVolume := calculateAverageVolume(volumes, params.VolumePeriod, index)

	// 检查上影线
	hasLongUpperShadow := checkLongUpperShadow(klines[index], params.UpperShadowRatio)

	// 如果没有持仓，检查入场条件
	if len(bt.TrendFollowPositions) == 0 {
		// 入场条件检查
		if isTrendBullish(currentEMAFast, currentEMASlow) &&
			isPriceAboveEMA(currentPrice, currentEMASlow) &&
			isRSIInRange(currentRSI, params.RSILowerBound, params.RSIUpperBound) &&
			!hasLongUpperShadow &&
			isVolumeAboveAverage(klines[index].Volume, avgVolume, params.VolumeMultiplier) {
			return BuyLong, currentPrice
		}
		return NoSignal, 0
	}

	// 有持仓时的逻辑
	// 检查止损条件
	if shouldStopLoss(currentPrice, currentEMASlow, params.StopLossPercent) {
		return SellLong, currentPrice
	}

	// 检查止盈条件
	totalPnL := bt.calculateTrendFollowPnL(currentPrice)
	totalInvestment := bt.TrendFollowTotalInvestment
	if totalInvestment > 0 {
		profitPercent := totalPnL / totalInvestment
		if profitPercent >= params.TakeProfitPercent {
			return SellLong, currentPrice
		}
	}

	// 检查RSI过热出场条件
	if currentRSI > params.RSIExitBound && hasLongUpperShadow {
		return SellLong, currentPrice
	}

	// 检查加仓条件
	if len(bt.TrendFollowPositions) < params.MaxAddPositions+1 {
		lastPosition := bt.TrendFollowPositions[len(bt.TrendFollowPositions)-1]
		callbackPrice := lastPosition.EntryPrice * (1 - params.CallbackPercent)

		if currentPrice <= callbackPrice &&
			isTrendBullish(currentEMAFast, currentEMASlow) {
			return BuyLong, currentPrice
		}
	}

	return NoSignal, 0
}

// 趋势跟随策略辅助函数

// isTrendBullish 判断是否为多头趋势
func isTrendBullish(emaFast, emaSlow float64) bool {
	return emaFast > emaSlow
}

// isPriceAboveEMA 判断价格是否在EMA之上
func isPriceAboveEMA(price, ema float64) bool {
	return price > ema
}

// isRSIInRange 判断RSI是否在指定范围内
func isRSIInRange(rsi, lowerBound, upperBound float64) bool {
	return rsi >= lowerBound && rsi <= upperBound
}

// isVolumeAboveAverage 判断成交量是否超过平均值
func isVolumeAboveAverage(currentVolume, avgVolume, multiplier float64) bool {
	return currentVolume > avgVolume*multiplier
}

// shouldStopLoss 判断是否应该止损
func shouldStopLoss(currentPrice, emaSlow, stopLossPercent float64) bool {
	// 跌破EMA20超过1.5%
	return currentPrice < emaSlow*(1-stopLossPercent)
}

// checkLongUpperShadow 检查是否有长上影线
func checkLongUpperShadow(kline *Kline, shadowRatio float64) bool {
	bodySize := math.Abs(kline.Close - kline.Open)
	upperShadow := kline.High - math.Max(kline.Open, kline.Close)

	if bodySize == 0 {
		return false
	}

	return upperShadow/bodySize > shadowRatio
}

// calculateAverageVolume 计算平均成交量
func calculateAverageVolume(volumes []float64, period, currentIndex int) float64 {
	if currentIndex < period-1 {
		return 0
	}

	sum := 0.0
	start := currentIndex - period + 1
	for i := start; i <= currentIndex; i++ {
		sum += volumes[i]
	}

	return sum / float64(period)
}

// BreakthroughParams 双向突破+高频止盈策略参数
type BreakthroughParams struct {
	EMAFast              int     // EMA快线周期（默认5）
	EMASlow              int     // EMA慢线周期（默认20）
	BollingerPeriod      int     // 布林带周期（默认20）
	BollingerStdDev      float64 // 布林带标准差倍数（默认2.0）
	BreakoutThreshold    float64 // 突破阈值（默认0.5%）
	TakeProfitMin        float64 // 最小止盈（默认1.0%）
	TakeProfitMax        float64 // 最大止盈（默认1.5%）
	StopLossMin          float64 // 最小止损（默认0.5%）
	StopLossMax          float64 // 最大止损（默认0.8%）
	PositionSize         float64 // 每笔仓位本金（默认50U）
	LeverageDefault      float64 // 默认杠杆（默认20x）
	LeverageMax          float64 // 最大杠杆（默认50x）
	MaxPositions         int     // 最大同时持仓数（默认3）
	DailyTarget          float64 // 每日收益目标（默认100U）
	MaxDrawdown          float64 // 最大回撤（默认100U）
	VolumeMultiplier     float64 // 成交量倍数（默认1.2）
	VolumePeriod         int     // 成交量平均周期（默认5）
	MomentumPeriod       int     // 动量周期（默认10）
	InitialBalance       float64 // 初始资金（默认500U）
}

// BreakthroughPosition 双向突破策略持仓信息
type BreakthroughPosition struct {
	ID           int     // 持仓ID
	Symbol       string  // 交易对
	Direction    string  // 方向 (LONG/SHORT)
	EntryPrice   float64 // 入场价格
	Size         float64 // 仓位大小（USDT）
	Leverage     float64 // 杠杆倍数
	TakeProfit   float64 // 止盈价格
	StopLoss     float64 // 止损价格
	Timestamp    int64   // 入场时间
	IsActive     bool    // 是否活跃
}

// CoinMomentum 币种动量信息
type CoinMomentum struct {
	Symbol        string  // 交易对
	PriceChange   float64 // 价格变化百分比
	VolumeChange  float64 // 成交量变化百分比
	MomentumScore float64 // 综合动量评分
}

// GetDefaultBreakthroughParams 获取默认双向突破策略参数
func GetDefaultBreakthroughParams() BreakthroughParams {
	return BreakthroughParams{
		EMAFast:           5,
		EMASlow:           20,
		BollingerPeriod:   20,
		BollingerStdDev:   2.0,
		BreakoutThreshold: 0.005, // 0.5%
		TakeProfitMin:     0.01,  // 1.0%
		TakeProfitMax:     0.015, // 1.5%
		StopLossMin:       0.005, // 0.5%
		StopLossMax:       0.008, // 0.8%
		PositionSize:      50.0,  // 50U
		LeverageDefault:   20.0,  // 20x
		LeverageMax:       50.0,  // 50x
		MaxPositions:      3,     // 最多3个仓位
		DailyTarget:       100.0, // 100U日收益目标
		MaxDrawdown:       100.0, // 100U最大回撤
		VolumeMultiplier:  1.2,   // 成交量1.2倍
		VolumePeriod:      5,     // 5周期成交量
		MomentumPeriod:    10,    // 10周期动量
		InitialBalance:    500.0, // 500U初始资金
	}
}

// BreakthroughStrategy 双向突破+高频止盈策略实现
func BreakthroughStrategy(klines []*Kline, bt *Backtest, index int, params BreakthroughParams, symbol string, allSymbolPrices map[string]float64) (TradeSignal, float64, string) {
	if index < params.BollingerPeriod {
		return NoSignal, 0, ""
	}

	// 设置双向突破策略标志
	bt.IsBreakthroughStrategy = true

	// 检查是否需要重置每日统计（简化：每1000个K线重置一次）
	if index%1000 == 0 {
		bt.resetBreakthroughDaily(klines[index].Timestamp)
	}

	// 检查是否达到每日目标或最大回撤
	if bt.checkBreakthroughDailyTarget(params) {
		// 达到每日目标，停止交易24小时
		return NoSignal, 0, ""
	}

	if bt.checkBreakthroughMaxDrawdown(params) {
		// 超过最大回撤，强制平仓并停止交易
		return SellLong, klines[index].Close, symbol // 这里简化处理，实际应该平所有仓位
	}

	// 检查当前持仓数量
	activePositions := bt.GetActiveBreakthroughPositions()
	if len(activePositions) >= params.MaxPositions {
		// 已达到最大持仓数，不再开新仓
		return NoSignal, 0, ""
	}

	// 选择最活跃的币种
	bestSymbol := selectBestCoin(allSymbolPrices, klines, index, params)
	if bestSymbol == "" {
		return NoSignal, 0, ""
	}

	// 计算技术指标
	closes := make([]float64, index+1)
	highs := make([]float64, index+1)
	lows := make([]float64, index+1)
	volumes := make([]float64, index+1)

	for i := 0; i <= index; i++ {
		closes[i] = klines[i].Close
		highs[i] = klines[i].High
		lows[i] = klines[i].Low
		volumes[i] = klines[i].Volume
	}

	// 计算EMA
	emaFast := talib.Ema(closes, params.EMAFast)
	emaSlow := talib.Ema(closes, params.EMASlow)

	if len(emaFast) == 0 || len(emaSlow) == 0 {
		return NoSignal, 0, ""
	}

	currentEMAFast := emaFast[len(emaFast)-1]
	currentEMASlow := emaSlow[len(emaSlow)-1]

	// 计算布林带
	upperBand, middleBand, lowerBand := talib.BBands(closes, params.BollingerPeriod, params.BollingerStdDev, params.BollingerStdDev, 0)
	if len(upperBand) == 0 {
		return NoSignal, 0, ""
	}

	currentUpperBand := upperBand[len(upperBand)-1]
	currentLowerBand := lowerBand[len(lowerBand)-1]
	_ = middleBand[len(middleBand)-1] // 暂时不使用中轨

	currentPrice := klines[index].Close

	// 计算成交量确认
	avgVolume := calculateAverageVolume(volumes, params.VolumePeriod, index)
	volumeConfirmed := klines[index].Volume > avgVolume*params.VolumeMultiplier

	// 检查突破条件
	upperBreakout := currentPrice > currentUpperBand*(1+params.BreakoutThreshold)
	lowerBreakout := currentPrice < currentLowerBand*(1-params.BreakoutThreshold)

	// 生成交易信号
	if upperBreakout && currentEMAFast > currentEMASlow && volumeConfirmed {
		// 上轨突破 + EMA多头 + 成交量确认 → 做多
		return BuyLong, currentPrice, bestSymbol
	} else if lowerBreakout && currentEMAFast < currentEMASlow && volumeConfirmed {
		// 下轨突破 + EMA空头 + 成交量确认 → 做空
		return BuyShort, currentPrice, bestSymbol
	}

	return NoSignal, 0, ""
}

// 双向突破策略辅助函数

// selectBestCoin 选择最活跃的币种
func selectBestCoin(allSymbolPrices map[string]float64, klines []*Kline, index int, params BreakthroughParams) string {
	// 支持的币种
	supportedSymbols := []string{"BTCUSDT", "ETHUSDT", "SOLUSDT", "BNBUSDT"}

	if len(allSymbolPrices) == 0 {
		// 如果没有多币种数据，默认返回BTC
		return "BTCUSDT"
	}

	// 计算各币种的动量评分
	coinMomentums := make([]CoinMomentum, 0, len(supportedSymbols))

	for _, symbol := range supportedSymbols {
		momentum := calculateCoinMomentum(symbol, klines, index, params)
		if momentum.MomentumScore > 0 {
			coinMomentums = append(coinMomentums, momentum)
		}
	}

	// 如果没有合适的币种，返回BTC
	if len(coinMomentums) == 0 {
		return "BTCUSDT"
	}

	// 选择动量评分最高的币种
	bestCoin := coinMomentums[0]
	for _, coin := range coinMomentums {
		if coin.MomentumScore > bestCoin.MomentumScore {
			bestCoin = coin
		}
	}

	return bestCoin.Symbol
}

// calculateCoinMomentum 计算币种动量
func calculateCoinMomentum(symbol string, klines []*Kline, index int, params BreakthroughParams) CoinMomentum {
	if index < params.MomentumPeriod {
		return CoinMomentum{Symbol: symbol, MomentumScore: 0}
	}

	// 计算价格变化
	startPrice := klines[index-params.MomentumPeriod].Close
	currentPrice := klines[index].Close
	priceChange := (currentPrice - startPrice) / startPrice

	// 计算成交量变化
	var oldVolumeSum, newVolumeSum float64
	halfPeriod := params.MomentumPeriod / 2

	for i := index - params.MomentumPeriod; i < index-halfPeriod; i++ {
		oldVolumeSum += klines[i].Volume
	}

	for i := index - halfPeriod; i <= index; i++ {
		newVolumeSum += klines[i].Volume
	}

	volumeChange := (newVolumeSum - oldVolumeSum) / oldVolumeSum

	// 综合评分：价格变化权重70%，成交量变化权重30%
	momentumScore := math.Abs(priceChange)*0.7 + math.Abs(volumeChange)*0.3

	return CoinMomentum{
		Symbol:        symbol,
		PriceChange:   priceChange,
		VolumeChange:  volumeChange,
		MomentumScore: momentumScore,
	}
}

// calculateDynamicLeverage 计算动态杠杆
func calculateDynamicLeverage(params BreakthroughParams, upperBand, lowerBand, middleBand, _ float64) float64 {
	// 计算布林带张口程度
	bandWidth := (upperBand - lowerBand) / middleBand

	// 布林带张口越大，波动越大，使用更高杠杆
	if bandWidth > 0.04 { // 4%以上张口，使用高杠杆
		return params.LeverageMax
	} else if bandWidth > 0.02 { // 2-4%张口，使用中等杠杆
		return (params.LeverageDefault + params.LeverageMax) / 2
	} else {
		return params.LeverageDefault
	}
}

// calculateDynamicStopLoss 计算动态止损
func calculateDynamicStopLoss(params BreakthroughParams, volatility float64) float64 {
	// 根据波动率调整止损
	if volatility > 0.03 { // 高波动
		return params.StopLossMax
	} else if volatility > 0.015 { // 中等波动
		return (params.StopLossMin + params.StopLossMax) / 2
	} else {
		return params.StopLossMin
	}
}

// calculateDynamicTakeProfit 计算动态止盈
func calculateDynamicTakeProfit(params BreakthroughParams, volatility float64) float64 {
	// 根据波动率调整止盈
	if volatility > 0.03 { // 高波动
		return params.TakeProfitMax
	} else if volatility > 0.015 { // 中等波动
		return (params.TakeProfitMin + params.TakeProfitMax) / 2
	} else {
		return params.TakeProfitMin
	}
}
