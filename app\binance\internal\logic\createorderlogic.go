package logic

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateOrderLogic {
	return &CreateOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateOrderLogic) CreateOrder(req *types.OrderRequest) (resp *types.OrderResponse, err error) {
	timestamp := time.Now().UnixMilli()
	params := url.Values{}
	params.Add("symbol", req.Symbol)
	params.Add("side", req.Side)
	params.Add("type", req.Type)
	params.Add("quantity", fmt.Sprintf("%.2f", req.Quantity))
	params.Add("price", fmt.Sprintf("%.2f", req.Price))
	params.Add("timestamp", fmt.Sprintf("%d", timestamp))

	query := params.Encode()
	signature := l.createSignature(query)

	url := fmt.Sprintf("%s/api/v3/order?%s&signature=%s", l.svcCtx.Config.BaseUrl.Http, query, signature)

	client := &http.Client{Timeout: 10 * time.Second}
	httpReq, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("X-MBX-APIKEY", l.svcCtx.Config.ApiKey)

	httpResp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer httpResp.Body.Close()

	var result struct {
		OrderId int64  `json:"orderId"`
		Symbol  string `json:"symbol"`
		Status  string `json:"status"`
	}
	if err := json.NewDecoder(httpResp.Body).Decode(&result); err != nil {
		return nil, err
	}

	return &types.OrderResponse{
		OrderId: result.OrderId,
		Symbol:  result.Symbol,
		Status:  result.Status,
	}, nil
}

func (l *CreateOrderLogic) createSignature(query string) string {
	h := hmac.New(sha256.New, []byte(l.svcCtx.Config.ApiSecret))
	h.Write([]byte(query))
	return hex.EncodeToString(h.Sum(nil))
}
