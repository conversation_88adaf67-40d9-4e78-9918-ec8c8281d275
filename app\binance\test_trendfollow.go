package main

import (
	"fmt"
	"math"
	"time"

	"zblockchain/app/binance/logic"
)

// 创建模拟K线数据 - 模拟BTC上涨趋势
func createTrendingKlines() []*logic.Kline {
	klines := make([]*logic.Kline, 200)
	basePrice := 50000.0
	timestamp := time.Now().Unix() * 1000

	for i := 0; i < 200; i++ {
		// 模拟上涨趋势，带有回调
		var priceChange float64
		if i < 50 {
			// 初期上涨
			priceChange = float64(i) * 100 + math.Sin(float64(i)*0.1)*500
		} else if i < 100 {
			// 中期回调
			priceChange = 5000 - float64(i-50)*50 + math.Sin(float64(i)*0.1)*300
		} else {
			// 后期强势上涨
			priceChange = 2500 + float64(i-100)*150 + math.Sin(float64(i)*0.1)*400
		}

		price := basePrice + priceChange
		
		// 模拟K线数据
		open := price + math.Sin(float64(i)*0.3)*100
		close := price + math.Cos(float64(i)*0.3)*100
		high := math.Max(open, close) + math.Abs(math.Sin(float64(i)*0.5))*200
		low := math.Min(open, close) - math.Abs(math.Cos(float64(i)*0.5))*150
		volume := 1000000 + math.Sin(float64(i)*0.2)*500000

		klines[i] = &logic.Kline{
			Timestamp: timestamp + int64(i)*3600000, // 每小时
			Open:      open,
			High:      high,
			Low:       low,
			Close:     close,
			Volume:    volume,
		}
	}

	return klines
}

// 创建模拟资金费率数据
func createTrendingFundingRates() []*logic.FundingRate {
	rates := make([]*logic.FundingRate, 25) // 8小时一次，200小时约25次
	timestamp := time.Now().Unix() * 1000

	for i := 0; i < 25; i++ {
		rates[i] = &logic.FundingRate{
			Timestamp: timestamp + int64(i)*8*3600000, // 每8小时
			Rate:      0.0001 + math.Sin(float64(i)*0.1)*0.0001, // 0.01%左右
		}
	}

	return rates
}

func testTrendFollowStrategy() {
	fmt.Println("=== 趋势跟随策略测试 ===")

	// 创建模拟数据
	klines := createTrendingKlines()
	fundingRates := createTrendingFundingRates()

	// 设置回测参数
	initialBalance := 10000.0
	feeRate := 0.04 / 100
	leverage := 2.0
	stopLossMultiplier := 1.5
	takeProfitMultiplier := 2.0
	maintenanceMarginRate := 0.005

	fmt.Printf("初始资金: %.2f USDT\n", initialBalance)
	fmt.Printf("杠杆倍数: %.1fx\n", leverage)
	fmt.Printf("手续费率: %.3f%%\n", feeRate*100)

	// 测试1: 默认参数
	fmt.Println("\n--- 测试1: 默认参数 ---")
	params := logic.GetDefaultTrendFollowParams(initialBalance)
	bt1 := logic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt1.Run(klines, fundingRates, func(klines []*logic.Kline, bt *logic.Backtest, index int) (logic.TradeSignal, float64) {
		return logic.TrendFollowStrategy(klines, bt, index, params)
	})

	fmt.Printf("最终资金: %.2f USDT\n", bt1.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt1.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt1.TradeCount)
	if bt1.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt1.WinCount)/float64(bt1.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt1.MaxDrawdown*100)
	fmt.Printf("持仓层数: %d\n", len(bt1.TrendFollowPositions))

	// 测试2: 保守型策略
	fmt.Println("\n--- 测试2: 保守型策略 ---")
	conservativeParams := logic.TrendFollowParams{
		EMAFast:              5,
		EMASlow:              20,
		InitialPositionRatio: 0.15,
		CallbackPercent:      0.015,
		MaxAddPositions:      1,
		MaxPositionRatio:     0.45,
		TakeProfitPercent:    0.04,
		StopLossPercent:      0.01,
		RSILowerBound:        45,
		RSIUpperBound:        65,
		RSIExitBound:         75,
		RSIPeriod:            14,
		VolumeMultiplier:     1.1,
		VolumePeriod:         5,
		UpperShadowRatio:     0.25,
	}

	bt2 := logic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt2.Run(klines, fundingRates, func(klines []*logic.Kline, bt *logic.Backtest, index int) (logic.TradeSignal, float64) {
		return logic.TrendFollowStrategy(klines, bt, index, conservativeParams)
	})

	fmt.Printf("最终资金: %.2f USDT\n", bt2.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt2.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt2.TradeCount)
	if bt2.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt2.WinCount)/float64(bt2.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt2.MaxDrawdown*100)
	fmt.Printf("持仓层数: %d\n", len(bt2.TrendFollowPositions))

	// 测试3: 激进型策略
	fmt.Println("\n--- 测试3: 激进型策略 ---")
	aggressiveParams := logic.TrendFollowParams{
		EMAFast:              5,
		EMASlow:              20,
		InitialPositionRatio: 0.25,
		CallbackPercent:      0.025,
		MaxAddPositions:      3,
		MaxPositionRatio:     0.75,
		TakeProfitPercent:    0.08,
		StopLossPercent:      0.02,
		RSILowerBound:        35,
		RSIUpperBound:        75,
		RSIExitBound:         85,
		RSIPeriod:            14,
		VolumeMultiplier:     1.3,
		VolumePeriod:         5,
		UpperShadowRatio:     0.35,
	}

	bt3 := logic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt3.Run(klines, fundingRates, func(klines []*logic.Kline, bt *logic.Backtest, index int) (logic.TradeSignal, float64) {
		return logic.TrendFollowStrategy(klines, bt, index, aggressiveParams)
	})

	fmt.Printf("最终资金: %.2f USDT\n", bt3.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt3.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt3.TradeCount)
	if bt3.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt3.WinCount)/float64(bt3.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt3.MaxDrawdown*100)
	fmt.Printf("持仓层数: %d\n", len(bt3.TrendFollowPositions))

	// 测试4: 参数优化
	fmt.Println("\n--- 测试4: 参数优化 ---")
	bestParams, bestBalance := logic.OptimizeTrendFollowParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	
	fmt.Printf("最佳参数:\n")
	fmt.Printf("  EMA快线: %d, EMA慢线: %d\n", bestParams.EMAFast, bestParams.EMASlow)
	fmt.Printf("  初始仓位: %.1f%%, 最大仓位: %.1f%%\n", bestParams.InitialPositionRatio*100, bestParams.MaxPositionRatio*100)
	fmt.Printf("  回调触发: %.1f%%, 最大加仓: %d次\n", bestParams.CallbackPercent*100, bestParams.MaxAddPositions)
	fmt.Printf("  止盈: %.1f%%, 止损: %.1f%%\n", bestParams.TakeProfitPercent*100, bestParams.StopLossPercent*100)
	fmt.Printf("最佳资金: %.2f USDT (收益率: %.2f%%)\n", bestBalance, (bestBalance-initialBalance)/initialBalance*100)

	// 比较结果
	fmt.Println("\n=== 策略对比 ===")
	fmt.Printf("默认策略: %.2f%% 收益\n", (bt1.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("保守策略: %.2f%% 收益\n", (bt2.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("激进策略: %.2f%% 收益\n", (bt3.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("优化策略: %.2f%% 收益\n", (bestBalance-initialBalance)/initialBalance*100)

	// 找出最佳策略
	bestReturn := (bt1.Balance-initialBalance)/initialBalance*100
	bestStrategy := "默认策略"
	
	if (bt2.Balance-initialBalance)/initialBalance*100 > bestReturn {
		bestReturn = (bt2.Balance-initialBalance)/initialBalance*100
		bestStrategy = "保守策略"
	}
	
	if (bt3.Balance-initialBalance)/initialBalance*100 > bestReturn {
		bestReturn = (bt3.Balance-initialBalance)/initialBalance*100
		bestStrategy = "激进策略"
	}
	
	if (bestBalance-initialBalance)/initialBalance*100 > bestReturn {
		bestReturn = (bestBalance-initialBalance)/initialBalance*100
		bestStrategy = "优化策略"
	}

	fmt.Printf("\n🏆 最佳策略: %s (收益率: %.2f%%)\n", bestStrategy, bestReturn)
}

// func main() {
// 	testTrendFollowStrategy()
// }
