package handler

import (
	"net/http"

	"zblockchain/app/bmgsdk/internal/logic"
	"zblockchain/app/bmgsdk/internal/svc"
	"zblockchain/app/bmgsdk/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func payResultHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.PayResultReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewPayResultLogic(r.Context(), svcCtx)
		resp, _ := l.PayResult(&req)
		// result.HttpResult(r, w, resp, err)
		httpx.OkJsonCtx(r.Context(), w, resp)
		// if err != nil {
		// 	httpx.ErrorCtx(r.Context(), w, err)
		// } else {
		// 	httpx.OkJsonCtx(r.Context(), w, resp)
		// }
	}
}
