#cd .\deploy\script\swagger\json\
#生成swagger.json 文件 指定Host，basePath
#usercenter
goctl api plugin -plugin goctl-swagger="swagger -filename usercenter.json -host **************:8886/ -basepath /" -api usercenter.api -dir ../../../../deploy/script/swagger/json
#crazysprial
goctl api plugin -plugin goctl-swagger="swagger -filename crazysprial.json -host **************:8886/ -basepath /" -api crazysprial.api -dir ../../../../deploy/script/swagger/json
#lucky777
goctl api plugin -plugin goctl-swagger="swagger -filename lucky777.json -host **************:8886/ -basepath /" -api lucky777.api -dir ../../../../deploy/script/swagger/json
#jinglefruits
goctl api plugin -plugin goctl-swagger="swagger -filename jinglefruits.json -host **************:8886/ -basepath /" -api jinglefruits.api -dir ../../../../deploy/script/swagger/json
#hotspin
goctl api plugin -plugin goctl-swagger="swagger -filename hotspin.json -host **************:8886/ -basepath /" -api ../../../../app/hotspin/api/desc/hotspin.api -dir .
#luckyspin
goctl api plugin -plugin goctl-swagger="swagger -filename luckyspin.json -host **************:8886/ -basepath /" -api ../../../../app/luckyspin/api/desc/luckyspin.api -dir .
#luckywheel
goctl api plugin -plugin goctl-swagger="swagger -filename luckywheel.json -host **************:8886/ -basepath /" -api ../../../../app/luckywheel/api/desc/luckywheel.api -dir .
#docker run -d -p 8080:8080 -e SWAGGER_JSON=/data/json/usercenter.json -v /data/work/zgame/deploy/script/swagger/json:/data/json swaggerapi/swagger-ui