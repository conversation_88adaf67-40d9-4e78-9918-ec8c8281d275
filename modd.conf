#usercenter
app/usercenter/rpc/**/*.go {
    prep: go build -o data/server/usercenter-rpc  -v app/usercenter/rpc/usercenter.go
    daemon +sigkill: ./data/server/usercenter-rpc -f app/usercenter/rpc/etc/usercenter.yaml
}
app/usercenter/api/**/*.go {
    prep: go build -o data/server/usercenter-api  -v app/usercenter/api/usercenter.go
    daemon +sigkill: ./data/server/usercenter-api -f app/usercenter/api/etc/usercenter.yaml
}
#crazysprial
app/crazysprial/rpc/**/*.go {
    prep: go build -o data/server/crazysprial-rpc  -v app/crazysprial/rpc/crazysprial.go
    daemon +sigkill: ./data/server/crazysprial-rpc -f app/crazysprial/rpc/etc/crazysprial.yaml
}
app/crazysprial/api/**/*.go {
    prep: go build -o data/server/crazysprial-api  -v app/crazysprial/api/crazysprial.go
    daemon +sigkill: ./data/server/crazysprial-api -f app/crazysprial/api/etc/crazysprial.yaml
}
#lucky777
app/lucky777/rpc/**/*.go {
    prep: go build -o data/server/lucky777-rpc  -v app/lucky777/rpc/lucky777.go
    daemon +sigkill: ./data/server/lucky777-rpc -f app/lucky777/rpc/etc/lucky777.yaml
}
app/lucky777/api/**/*.go {
    prep: go build -o data/server/lucky777-api  -v app/lucky777/api/lucky777.go
    daemon +sigkill: ./data/server/lucky777-api -f app/lucky777/api/etc/lucky777.yaml
}
#jinglefruits
app/jinglefruits/rpc/**/*.go {
    prep: go build -o data/server/jinglefruits-rpc  -v app/jinglefruits/rpc/jinglefruits.go
    daemon +sigkill: ./data/server/jinglefruits-rpc -f app/jinglefruits/rpc/etc/jinglefruits.yaml
}
app/jinglefruits/api/**/*.go {
    prep: go build -o data/server/jinglefruits-api  -v app/jinglefruits/api/jinglefruits.go
    daemon +sigkill: ./data/server/jinglefruits-api -f app/jinglefruits/api/etc/jinglefruits.yaml
}
#hotspin
app/hotspin/rpc/**/*.go {
    prep: go build -o data/server/hotspin-rpc  -v app/hotspin/rpc/hotspin.go
    daemon +sigkill: ./data/server/hotspin-rpc -f app/hotspin/rpc/etc/hotspin.yaml
}
app/hotspin/api/**/*.go {
    prep: go build -o data/server/hotspin-api  -v app/hotspin/api/hotspin.go
    daemon +sigkill: ./data/server/hotspin-api -f app/hotspin/api/etc/hotspin.yaml
}
#luckyspin
app/luckyspin/rpc/**/*.go {
    prep: go build -o data/server/luckyspin-rpc  -v app/luckyspin/rpc/luckyspin.go
    daemon +sigkill: ./data/server/luckyspin-rpc -f app/luckyspin/rpc/etc/luckyspin.yaml
}
app/luckyspin/api/**/*.go {
    prep: go build -o data/server/luckyspin-api  -v app/luckyspin/api/luckyspin.go
    daemon +sigkill: ./data/server/luckyspin-api -f app/luckyspin/api/etc/luckyspin.yaml
}
#luckywheel
app/luckywheel/rpc/**/*.go {
    prep: go build -o data/server/luckywheel-rpc  -v app/luckywheel/rpc/luckywheel.go
    daemon +sigkill: ./data/server/luckywheel-rpc -f app/luckywheel/rpc/etc/luckywheel.yaml
}
app/luckywheel/api/**/*.go {
    prep: go build -o data/server/luckywheel-api  -v app/luckywheel/api/luckywheel.go
    daemon +sigkill: ./data/server/luckywheel-api -f app/luckywheel/api/etc/luckywheel.yaml
}

