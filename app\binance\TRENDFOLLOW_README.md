# 趋势跟随+回调加仓策略 (BTC做多策略)

## 策略概述

这是一个专为小资金BTC做多设计的趋势跟随策略，结合EMA趋势判断、RSI过滤、成交量确认和回调加仓机制，旨在在上涨趋势中获取稳定收益。

## 策略逻辑

### 1. 趋势判断
- **EMA5 > EMA20**: 判断为多头趋势
- **价格 > EMA20**: 确认价格在趋势线之上
- **RSI 40-70**: 避免超买超卖区域入场

### 2. 入场条件
- 当前处于多头趋势 (EMA5 > EMA20)
- 价格大于EMA20
- RSI在40-70之间
- 无长上影线 (上影线/实体 < 30%)
- 成交量大于过去5个周期平均成交量的1.2倍

### 3. 仓位管理
- **初始开仓**: 20%资金
- **回调加仓**: 价格回调2%时加仓，最多加仓2次
- **最大持仓**: 60%资金 (剩余40%作为风险准备金)
- **建议杠杆**: 1-2倍

### 4. 止盈止损
- **止盈**: 盈利达到6%时平仓
- **止损**: 跌破EMA20超过1.5%时平仓
- **RSI过热**: RSI > 80且出现长上影线时提前止盈

## 参数配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| EMA快线 | 5 | 短期趋势线 |
| EMA慢线 | 20 | 长期趋势线 |
| 初始仓位比例 | 20% | 首次开仓资金比例 |
| 回调加仓触发 | 2% | 从上次入场价回调的百分比 |
| 最大加仓次数 | 2次 | 最多持有3笔仓位 |
| 最大持仓比例 | 60% | 总资金使用上限 |
| 止盈点 | 6% | 盈利目标 |
| 止损点 | 1.5% | 风险控制 |
| RSI入场范围 | 40-70 | 避免极端区域 |
| RSI出场界限 | 80 | 过热出场 |
| 成交量倍数 | 1.2 | 成交量确认倍数 |
| 上影线比例 | 30% | 长上影线判断阈值 |

## API接口

### 1. 趋势跟随策略回测
```bash
POST /trendfollow/backtest
Content-Type: application/json

{
  "symbol": "BTCUSDT",
  "interval": "1h",
  "startTime": 1640995200000,
  "endTime": 1672531200000,
  "useOptimize": true
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "趋势跟随策略回测完成",
  "initialBalance": 10000,
  "finalBalance": 11200,
  "totalReturn": 12.0,
  "tradeCount": 15,
  "winRate": 66.7,
  "maxDrawdown": 5.2,
  "stopLossCount": 2,
  "takeProfitCount": 8,
  "bestParams": {
    "symbol": "BTCUSDT",
    "emaFast": 5,
    "emaSlow": 20,
    "initialPositionRatio": 0.2,
    "callbackPercent": 0.02,
    "maxAddPositions": 2,
    "maxPositionRatio": 0.6,
    "takeProfitPercent": 0.06,
    "stopLossPercent": 0.015,
    "rsiLowerBound": 40,
    "rsiUpperBound": 70,
    "rsiExitBound": 80,
    "rsiPeriod": 14,
    "volumeMultiplier": 1.2,
    "volumePeriod": 5,
    "upperShadowRatio": 0.3
  },
  "positions": []
}
```

### 2. 策略配置
```bash
POST /trendfollow/config
Content-Type: application/json

{
  "symbol": "BTCUSDT",
  "emaFast": 5,
  "emaSlow": 20,
  "initialPositionRatio": 0.2,
  "callbackPercent": 0.02,
  "maxAddPositions": 2,
  "maxPositionRatio": 0.6,
  "takeProfitPercent": 0.06,
  "stopLossPercent": 0.015,
  "rsiLowerBound": 40,
  "rsiUpperBound": 70,
  "rsiExitBound": 80,
  "rsiPeriod": 14,
  "volumeMultiplier": 1.2,
  "volumePeriod": 5,
  "upperShadowRatio": 0.3
}
```

### 3. 策略状态查询
```bash
GET /trendfollow/status
```

## 使用方法

### 1. 启动服务
```bash
cd app/binance
go run binance.go
```

### 2. 运行策略测试
```bash
# 运行趋势跟随策略测试
go run binance.go -trendfollow

# 运行独立测试
go run test_trendfollow.go
```

### 3. API调用示例
```bash
# 回测趋势跟随策略
curl -X POST http://localhost:8888/trendfollow/backtest \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "BTCUSDT",
    "interval": "1h",
    "startTime": 1640995200000,
    "endTime": 1672531200000,
    "useOptimize": true
  }'

# 配置策略参数
curl -X POST http://localhost:8888/trendfollow/config \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "BTCUSDT",
    "emaFast": 5,
    "emaSlow": 20,
    "initialPositionRatio": 0.2,
    "callbackPercent": 0.02,
    "maxAddPositions": 2,
    "maxPositionRatio": 0.6,
    "takeProfitPercent": 0.06,
    "stopLossPercent": 0.015,
    "rsiLowerBound": 40,
    "rsiUpperBound": 70,
    "rsiExitBound": 80,
    "rsiPeriod": 14,
    "volumeMultiplier": 1.2,
    "volumePeriod": 5,
    "upperShadowRatio": 0.3
  }'

# 查询策略状态
curl http://localhost:8888/trendfollow/status
```

## 策略优势

1. **趋势跟随**: 只在明确的上涨趋势中做多，避免震荡市场
2. **多重过滤**: EMA+RSI+成交量+K线形态多重确认
3. **风险控制**: 严格的止损机制和仓位管理
4. **回调加仓**: 利用趋势中的回调增加仓位
5. **资金管理**: 最多使用60%资金，保留40%风险准备金

## 风险提示

1. **趋势反转风险**: 强势上涨后可能出现急跌
2. **回调风险**: 回调幅度可能超过预期
3. **杠杆风险**: 建议使用1-2倍杠杆控制风险
4. **资金费率**: 长期持仓需考虑资金费率成本
5. **市场环境**: 适合趋势市场，不适合震荡市场

## 回测验证指标

- **胜率**: 目标 > 50%
- **年化收益率**: 目标 > 20%
- **最大回撤**: 目标 < 10%
- **单笔最大亏损**: 控制在2%以内

## 技术特点

1. **模块化设计**: 策略、回测、优化器分离
2. **参数优化**: 预设3种策略类型 (保守、平衡、激进)
3. **统一协程管理**: 使用common/routine统一管理
4. **完善的API**: RESTful API设计，易于集成
5. **详细日志**: 完整的交易记录和性能指标

## 下一步开发

1. **实盘交易**: 集成币安API实现实盘交易
2. **动态参数**: 根据市场波动率动态调整参数
3. **多币种支持**: 扩展到其他主流币种
4. **风险管理**: 增加更多风险控制机制
5. **策略组合**: 与马丁策略组合使用
