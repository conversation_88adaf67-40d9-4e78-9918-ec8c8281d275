package logic

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"zblockchain/app/bmgsdk/internal/svc"
	"zblockchain/app/bmgsdk/internal/types"
	"zblockchain/common/tool"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpc"
)

type LoginVerifyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// login
func NewLoginVerifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginVerifyLogic {
	return &LoginVerifyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LoginVerifyLogic) LoginVerify(req *types.LoginReq) (*types.LoginResp, error) {
	// todo: add your logic here and delete this line
	logx.Infof("LoginVerify :%v", req)
	type Request struct {
		// Node      string `path:"node"`
		Token     string `json:"token"`
		OpenId    string `json:"openId"`
		PartnerId string `json:"partnerId"`
		Timestamp int64  `json:"timestamp"`
		Sign      string `json:"sign"`
	}
	resp := &types.LoginResp{
		Code: 1,
	}
	nowN := time.Now()
	nowM := nowN.UnixNano() / int64(time.Second)
	params := []struct {
		Key   string
		Value interface{}
	}{
		{"partnerId", l.svcCtx.Config.ApiConfig.PartnerId},
		{"openId", req.OpenId},
		{"token", req.Token},
		{"timestamp", nowM},
	}

	sign := tool.ApiSign(params, l.svcCtx.Config.ApiConfig.LoginSecret, "", false, false)
	reqApi := Request{
		Token:     req.Token,
		OpenId:    req.OpenId,
		PartnerId: l.svcCtx.Config.ApiConfig.PartnerId,
		Timestamp: nowM,
		Sign:      sign,
	}
	respApi, err := httpc.Do(context.Background(), http.MethodPost, l.svcCtx.Config.ApiConfig.Url, reqApi)
	if err != nil {
		return &types.LoginResp{
			Code:    2,
			Message: err.Error(),
		}, err
	}
	body, err := io.ReadAll(respApi.Body)
	if err != nil {
		return &types.LoginResp{
			Code:    2,
			Message: err.Error(),
		}, err
	}
	logx.Infof("LoginVerify Rsp body:%s", string(body))
	_ = json.Unmarshal(body, resp)
	if resp.Code != 1 {
		return resp, nil
	}
	whereBuilder := l.svcCtx.VipModel.RowBuilder().Where(
		"openId = ? ",
		req.OpenId,
	)
	vipArry, err := l.svcCtx.VipModel.FindByQuery(l.ctx, whereBuilder)
	if err != nil {
		resp.Code = 2
		logx.Errorf("vipResult db err:%v,%v", vipArry, err)
	}

	for _, vip := range vipArry {
		if vip.ValidTime > 0 {
			duration := nowN.Sub(vip.UpdateTime).Seconds()
			vip.ValidTime = vip.ValidTime - int64(duration)
			if vip.ValidTime < 0 {
				vip.ValidTime = 0
			}
			if err := l.svcCtx.VipModel.Update(l.ctx, vip); err != nil {
				logx.Errorf("update vip faild! value:%v, err:%v", vip, err)
			}
		}
		rVip := &types.Vip{
			Id:   vip.VipId,
			Time: vip.ValidTime,
		}
		resp.VipInfo = append(resp.VipInfo, rVip)
	}
	logx.Infof("LoginVerify Rsp:%v", resp)
	return resp, nil
}
