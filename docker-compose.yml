version: '3'

######## app下api+rpc ,  Before starting this project, start the environment that the project depends on docker-compose-env.yml #######

services:
  mysql:
    image: mysql/mysql-server:8.0.28
    container_name: mysql
    environment:
      # 时区上海 - Time zone Shanghai (Change if needed)
      TZ: Asia/Shanghai
      # root 密码 - root password
      MYSQL_ROOT_PASSWORD: L9CTENWNrrjRL9CTENWNrrjR
    ports:
      - 3306:3306
    volumes:
      # 数据挂载 - Data mounting
      - ./data/mysql/data:/var/lib/mysql
      # 日志
    command:
      # 将mysql8.0默认密码策略 修改为 原先 策略 (mysql8.0对其默认策略做了更改 会导致密码无法匹配)
      # Modify the Mysql 8.0 default password strategy to the original strategy (MySQL8.0 to change its default strategy will cause the password to be unable to match)
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
    privileged: true
    restart: always
  #前端网关nginx-gateay (只代理zgame，admin-api不在这里做代理)
  # Front-end gateway nginx-gateway (Only agent zgame，admin-api Do not be an agent here)
  nginx-gateway:
    image: nginx:1.21.5
    container_name: nginx-gateway
    restart: always
    privileged: true
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 3000:3000
      - 8088:8088
    volumes:
      - ./deploy/nginx/conf.d:/etc/nginx/conf.d
      - ./data/nginx/log:/var/log/nginx
      - ./data/nginx/www:/www

  bmgsdk:
    image: alpine:latest
    container_name: bmgsdk
    environment:
      # 时区上海 - Timezone Shanghai
      TZ: Asia/Shanghai
    working_dir: /service
    volumes:
      - ./data/service:/service
    command: ./bmgsdk -f conf/bmgsdk.yaml
    privileged: true
    restart: always
