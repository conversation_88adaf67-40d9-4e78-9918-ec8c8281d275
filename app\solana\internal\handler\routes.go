// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.3

package handler

import (
	"net/http"

	wallet "zblockchain/app/solana/internal/handler/wallet"
	"zblockchain/app/solana/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/balance",
				Handler: wallet.BalanceHandler(serverCtx),
			},
		},
		rest.WithPrefix("/solana"),
	)
}
