package tcpnbio

import (
	"fmt"
	"net/http"
	"sync"
	"zblockchain/common/network/core"

	"github.com/lesismal/nbio"
	"github.com/zeromicro/go-zero/core/logx"
)

type Server struct {
	s *http.Server

	addr         string
	newAgent     core.GetAgent
	maxConnNum   int
	connMap      map[*nbio.Conn]*Conn
	connMapMutex sync.Mutex
}

// Start 开始tcp监听
func (server *Server) Start(address string, newAgent core.GetAgent, opts ...core.ServerOption) error {
	// 读取并初始化参数
	if newAgent == nil {
		return core.ErrInvalidGetAgentFunc
	}
	if !core.VerifyAddress(address) {
		return core.ErrInvalidAddress
	}
	server.newAgent = newAgent
	server.addr = address
	options := core.DefaultServerOptions
	for _, o := range opts {
		o(&options)
	}
	if options.MaxConnNum < 0 {
		server.maxConnNum = core.DefaultMaxConnNum
	} else {
		server.maxConnNum = options.MaxConnNum
	}
	server.connMap = make(map[*nbio.Conn]*Conn, 0)
	logx.Infof("WebSocket Listen %s", server.addr)

	return nil
}

// Run 执行服务端逻辑
func (server *Server) Run() {
	engine := nbio.NewEngine(nbio.Config{
		Network:            "tcp",
		Addrs:              []string{server.addr},
		MaxWriteBufferSize: 6 * 1024 * 1024,
	})
	engine.OnData(server.OnData)
	engine.OnOpen(server.OnOpen)
	engine.OnClose(server.OnClose)
	err := engine.Start()
	if err != nil {
		fmt.Printf("nbio.Start failed: %v\n", err)
		return
	}
}

// GetConnNum 获取所有连接的数量
func (server *Server) GetConnNum() int {
	return len(server.connMap)
}

func (server *Server) OnData(conn *nbio.Conn, data []byte) {
	if c, ok := server.connMap[conn]; ok {
		c.agent.OnMessage(data)
		return
	}
}

func (server *Server) OnOpen(conn *nbio.Conn) {
	agent := server.newAgent()
	if agent == nil {
		err := conn.Close()
		if err != nil {
			logx.Error("agent is nil, close conn error:", err)
		}
		return
	}
	c := newConn(conn, agent)
	server.connMapMutex.Lock()
	server.connMap[conn] = c
	server.connMapMutex.Unlock()
	agent.OnConnect(c)
}

func (server *Server) OnClose(conn *nbio.Conn, err error) {
	c := server.connMap[conn]
	if c == nil {
		return
	}

	c.Close()
	server.connMapMutex.Lock()
	delete(server.connMap, conn)
	server.connMapMutex.Unlock()
}

func (server *Server) Close() {
	if server.s == nil {
		return
	}

	err := server.s.Close()
	if err != nil {
		logx.Error("close server error:", err)
	}
	server.s = nil
}
