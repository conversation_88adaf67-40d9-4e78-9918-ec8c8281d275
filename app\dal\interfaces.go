package dal

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// Storage 定义所有存储操作的统一接口
type Storage interface {
	// Redis操作
	Redis() *redis.Redis
	Set(key, value string) error
	HSet(key, field, value string) error
	HMSet(key string, value map[string]string) error
	Get(key string) (string, error)
	HGet(key, field string) string
	HMGet(key string, fields ...string) []string
	HGetAll(key string) map[string]string
	Del(key string) error
	Exists(key string) bool
	Expire(key string, seconds int) error
	HKeys(key string) []string
	HDel(key, field string) error

	DB() sqlx.SqlConn
}
