package tcpnbio

import (
	"zblockchain/common/network/core"

	"github.com/lesismal/nbio"
	"github.com/zeromicro/go-zero/core/logx"
)

type Conn struct {
	*nbio.Conn
	agent core.Agent
}

func (c *Conn) Run() {
}

func (c *Conn) Close() {
	err := c.Conn.Close()
	if err != nil {
		logx.Debug("close websocket conn error: %v", err)
	}
	c.agent.OnClose()
}

func (c *Conn) Write(b []byte) (n int, err error) {
	len_, err := c.Conn.Write(b)
	return len_, err
}

func newConn(c *nbio.Conn, agent core.Agent) *Conn {
	return &Conn{
		Conn:  c,
		agent: agent,
	}
}

// setAgent 设置 Agent
func (c *Conn) setAgent(agent core.Agent) {
	c.agent = agent
}
