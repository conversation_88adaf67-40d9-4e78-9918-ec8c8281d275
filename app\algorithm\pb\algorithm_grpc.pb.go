// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.2
// source: algorithm.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Algorithm_MathRes_FullMethodName = "/pb.Algorithm/MathRes"
)

// AlgorithmClient is the client API for Algorithm service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AlgorithmClient interface {
	MathRes(ctx context.Context, in *MathReq, opts ...grpc.CallOption) (*MathResp, error)
}

type algorithmClient struct {
	cc grpc.ClientConnInterface
}

func NewAlgorithmClient(cc grpc.ClientConnInterface) AlgorithmClient {
	return &algorithmClient{cc}
}

func (c *algorithmClient) MathRes(ctx context.Context, in *MathReq, opts ...grpc.CallOption) (*MathResp, error) {
	out := new(MathResp)
	err := c.cc.Invoke(ctx, Algorithm_MathRes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AlgorithmServer is the server API for Algorithm service.
// All implementations must embed UnimplementedAlgorithmServer
// for forward compatibility
type AlgorithmServer interface {
	MathRes(context.Context, *MathReq) (*MathResp, error)
	mustEmbedUnimplementedAlgorithmServer()
}

// UnimplementedAlgorithmServer must be embedded to have forward compatible implementations.
type UnimplementedAlgorithmServer struct {
}

func (UnimplementedAlgorithmServer) MathRes(context.Context, *MathReq) (*MathResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MathRes not implemented")
}
func (UnimplementedAlgorithmServer) mustEmbedUnimplementedAlgorithmServer() {}

// UnsafeAlgorithmServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AlgorithmServer will
// result in compilation errors.
type UnsafeAlgorithmServer interface {
	mustEmbedUnimplementedAlgorithmServer()
}

func RegisterAlgorithmServer(s grpc.ServiceRegistrar, srv AlgorithmServer) {
	s.RegisterService(&Algorithm_ServiceDesc, srv)
}

func _Algorithm_MathRes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MathReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlgorithmServer).MathRes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Algorithm_MathRes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlgorithmServer).MathRes(ctx, req.(*MathReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Algorithm_ServiceDesc is the grpc.ServiceDesc for Algorithm service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Algorithm_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.Algorithm",
	HandlerType: (*AlgorithmServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MathRes",
			Handler:    _Algorithm_MathRes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "algorithm.proto",
}
