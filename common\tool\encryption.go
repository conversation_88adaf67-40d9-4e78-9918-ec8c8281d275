package tool

import (
	"crypto/md5"
	"fmt"
	"io"
	"sort"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
)

/** 加密方式 **/

func Md5ByString(str string) string {
	m := md5.New()
	_, err := io.WriteString(m, str)
	if err != nil {
		panic(err)
	}
	arr := m.Sum(nil)
	return fmt.Sprintf("%x", arr)
}

func Md5ByBytes(b []byte) string {
	return fmt.Sprintf("%x", md5.Sum(b))
}

// stringValue 将 interface{} 转换为字符串
func stringValue(value interface{}) string {
	switch v := value.(type) {
	case string:
		return v
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%f", v)
	case bool:
		return fmt.Sprintf("%t", v)
	// 添加其他类型的处理
	default:
		return ""
	}
}

func ApiSign(params []struct {
	Key   string
	Value interface{}
}, secretKey,join string, bSort,isUrl bool) string {
	var items []string
	if bSort{
		// 对参数按键名进行字母顺序排序
		sort.Slice(params, func(i, j int) bool {
			return params[i].Key < params[j].Key
		})
	}
	for _, param := range params {
		if param.Key != "sign" {
			if isUrl{
				items = append(items, param.Key+"="+stringValue(param.Value))
			}else{
				items = append(items, stringValue(param.Value))
			}
		}
	}
	// 将键值对连接成字符串
	text := strings.Join(items, join)
	sign := Md5ByString(text + secretKey)
	logx.Errorf("ApiSign:%s | %s", text+secretKey, sign)
	return sign
}


