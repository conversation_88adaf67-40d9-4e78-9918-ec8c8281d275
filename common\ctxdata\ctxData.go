package ctxdata

import (
	"context"
	"encoding/json"
	"strconv"
	"zblockchain/common/xerr"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
)

// CtxKeyJwtUserId get uid from ctx
var CtxKeyJwtUserId = "jwtUserId"
var CtxKeyJwtUserVer = "jwtUserVer"

//func GetUidFromCtx(ctx context.Context) (int64, int64) {
//	var uid int64
//	var version int64
//	if jsonUid, ok := ctx.Value(CtxKeyJwtUserId).(json.Number); ok {
//		if int64Uid, err := jsonUid.Int64(); err == nil {
//			uid = int64Uid
//		} else {
//			logx.WithContext(ctx).Errorf("GetUidFromCtx err : %+v", err)
//		}
//	}
//	if jsonVer, ok := ctx.Value(CtxKeyJwtUserVer).(json.Number); ok {
//		if int64Ver, err := jsonVer.Int64(); err == nil {
//			version = int64Ver
//		} else {
//			logx.WithContext(ctx).Errorf("GetUidFromCtx err : %+v", err)
//		}
//	}
//	return uid, version
//}

func GetUidFromCtx(ctx context.Context, redisClient *redis.Redis) (int64, error) {
	var uid int64
	if jsonUid, ok := ctx.Value(CtxKeyJwtUserId).(json.Number); ok {
		if int64Uid, err := jsonUid.Int64(); err == nil {
			uid = int64Uid
		} else {
			logx.WithContext(ctx).Errorf("GetUidFromCtx err : %+v", err)
			return 0, err
		}
	}
	var version string
	if jsonVer, ok := ctx.Value(CtxKeyJwtUserVer).(json.Number); ok {
		version = jsonVer.String()
		ver, err := redisClient.Get(CtxKeyJwtUserVer + ":" + strconv.FormatInt(uid, 10))
		if err != nil {
			logx.WithContext(ctx).Errorf("GetUidFromCtx redis err : %+v", err)
			return 0, err
		}
		if len(ver) < 6 || ver != version {
			return 0, errors.Wrapf(xerr.NewErrCode(xerr.TOKEN_EXPIRE_ERROR), "token error")
		}
	} else {
		return 0, errors.Wrapf(xerr.NewErrCode(xerr.TOKEN_EXPIRE_ERROR), "token error")
	}
	return uid, nil
}
