{"swagger": "2.0", "info": {"title": "lucky777服务", "description": "lucky777服务", "version": ""}, "host": "**************:8886/", "basePath": "/", "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"lucky777/record": {"post": {"summary": "spin record", "operationId": "record", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/RecordResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RecordReq"}}], "requestBody": {}, "tags": ["spin"]}}, "lucky777/spin": {"post": {"summary": "spin", "operationId": "spin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SpinResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SpinReq"}}], "requestBody": {}, "tags": ["spin"]}}}, "definitions": {"Record": {"type": "object", "properties": {"betAmount": {"type": "number", "format": "double"}, "winnings": {"type": "number", "format": "double"}, "dateTime": {"type": "integer", "format": "int64"}}, "title": "Record", "required": ["betAmount", "winnings", "dateTime"]}, "RecordReq": {"type": "object", "title": "RecordReq"}, "RecordResp": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/Record"}}}, "title": "RecordResp", "required": ["records"]}, "Result": {"type": "object", "properties": {"index1": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "index2": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "index3": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "title": "Result", "required": ["index1", "index2", "index3"]}, "SpinReq": {"type": "object", "properties": {"spinMoney": {"type": "number", "format": "double", "description": "下注金额"}}, "title": "SpinReq", "required": ["spinMoney"]}, "SpinResp": {"type": "object", "properties": {"resultIndexs": {"$ref": "#/definitions/Result", "description": "结果索引 （1:x100 2:777 3:钻石 4:红bar 5:紫bar 6:蓝bar 7:铃铛 8:柠檬 9：樱桃）"}, "win": {"type": "number", "format": "double", "description": "奖励"}, "money": {"type": "number", "format": "double", "description": "余额"}}, "title": "SpinResp", "required": ["resultIndexs", "win", "money"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}, "security": [{"apiKey": []}]}