post请求

http://49.0.204.134:3000/bmgsdk/loginVerify

{
  "token":"124141",
  "openId": "openId-001"
}

返回
{
  "code":1,  //1: 成功 2: 失败
  "message": "",
  "data":{
	  "nickName": "abcdef",
	  "picUrl": "头像图片http地址",
	  "sex": 1, // 性别, 1 表示男, 2 表示女
	  "birthday": "2022-11-11"
  }
}

post 请求
http://49.0.204.134:3000/bmgsdk/payResult

{
  "openId": "openId-001",
  "orderId": "12314"
}

返回
{
  "code":1  //1: 成功 2: 失败
}