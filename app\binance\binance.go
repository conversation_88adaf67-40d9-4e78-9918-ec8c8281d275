package main

import (
	"flag"
	"fmt"

	"zblockchain/app/binance/config"
	"zblockchain/app/binance/internal/handler"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/logic"
	"zblockchain/app/dal"

	"github.com/zeromicro/go-zero/rest"
)

func main() {
	// 添加命令行参数支持
	var martingaleTest = flag.Bool("martingale", false, "运行马丁策略测试")
	var trendFollowTest = flag.Bool("trendfollow", false, "运行趋势跟随策略测试")
	var breakthroughTest = flag.Bool("breakthrough", false, "运行双向突破策略测试")
	flag.Parse()

	config.LoadConfig()

	// 如果指定了马丁策略测试参数，运行测试并退出
	if *martingaleTest {
		fmt.Println("运行马丁策略测试...")
		logic.RunMartingale()
		return
	}

	// 如果指定了趋势跟随策略测试参数，运行测试并退出
	if *trendFollowTest {
		fmt.Println("运行趋势跟随策略测试...")
		logic.RunTrendFollow()
		return
	}

	// 如果指定了双向突破策略测试参数，运行测试并退出
	if *breakthroughTest {
		fmt.Println("运行双向突破策略测试...")
		logic.RunBreakthrough()
		return
	}

	server := rest.MustNewServer(config.GlobalConfig.RestConf, rest.WithCors())
	defer server.Stop()

	ctx := svc.NewServiceContext(config.GlobalConfig)
	handler.RegisterHandlers(server, ctx)

	dal.Init(ctx.Redis, ctx.DB)

	fmt.Printf("Starting server at %s:%d...\n", config.GlobalConfig.Host, config.GlobalConfig.Port)
	fmt.Println("马丁策略API已启用:")
	fmt.Println("  POST /martingale/backtest - 马丁策略回测")
	fmt.Println("  POST /martingale/config - 马丁策略配置")
	fmt.Println("  GET  /martingale/status - 马丁策略状态")
	server.Start()
}
