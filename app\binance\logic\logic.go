package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strconv"
	"time"
	"zblockchain/app/binance/config"
	"zblockchain/app/dal"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/stores/redis"
)

func Run() {
	// 配置币安 API（需真实 API 密钥获取历史资金费率）
	client := futures.NewClient(config.GlobalConfig.ApiKey, config.GlobalConfig.ApiSecret) // 填入 API Key 和 Secret Key

	// 设置回测参数
	symbol := "BTCUSDT"
	interval := "1h"
	startTime := time.Now().AddDate(0, -3, 0).Unix() * 1000 // 3 个月前
	endTime := time.Now().Unix() * 1000                     // 当前时间
	leverage := 10.0                                        // 杠杆倍数
	initialBalance := 10000.0                               // 初始资金 (USDT)
	feeRate := 0.04 / 100                                   // 交易手续费率 0.04%
	stopLossMultiplier := 1.5                               // ATR 止损倍数
	takeProfitMultiplier := 2.0                             // ATR 止盈倍数
	maintenanceMarginRate := 0.005                          // 维持保证金率 0.5%

	// 获取历史 K 线数据
	klines, err := GetHistoricalKlines(client, symbol, interval, startTime, endTime)
	if err != nil {
		log.Fatalf("获取 K 线数据失败: %v", err)
	}

	// 获取历史资金费率
	fundingRates, err := GetHistoricalFundingRates(client, symbol, startTime, endTime)
	if err != nil {
		log.Fatalf("获取资金费率失败: %v", err)
	}

	// 运行参数优化
	bestParams, bestBalance := OptimizeRSIParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	fmt.Printf("最佳参数: RSI周期=%d, 买入阈值=%.2f, 卖出阈值=%.2f, 止损ATR倍数=%.2f, 止盈ATR倍数=%.2f\n",
		bestParams.Period, bestParams.BuyThreshold, bestParams.SellThreshold, bestParams.StopLossMultiplier, bestParams.TakeProfitMultiplier)
	fmt.Printf("最佳资金: %.2f USDT (收益率: %.2f%%)\n", bestBalance, (bestBalance-initialBalance)/initialBalance*100)

	// 使用最佳参数运行回测
	bt := NewBacktest(initialBalance, feeRate, leverage, bestParams.StopLossMultiplier, bestParams.TakeProfitMultiplier, maintenanceMarginRate)
	bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
		return RSIStrategy(klines, bt, index, bestParams)
	})

	// 输出详细结果
	fmt.Printf("最终资金: %.2f USDT\n", bt.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt.TradeCount)
	fmt.Printf("胜率: %.2f%%\n", float64(bt.WinCount)/float64(bt.TradeCount)*100)
	fmt.Printf("最大回撤: %.2f%%\n", bt.MaxDrawdown*100)
	fmt.Printf("止损触发次数: %d\n", bt.StopLossCount)
	fmt.Printf("止盈触发次数: %d\n", bt.TakeProfitCount)
	fmt.Printf("清算次数: %d\n", bt.LiquidationCount)
}

func GetHistoricalKlines(client *futures.Client, symbol, interval string, startTime, endTime int64) ([]*Kline, error) {
	// 生成 Redis key
	redisKey := fmt.Sprintf("kline:%s:%s", symbol, interval)

	// 计算时间戳列表（按 K 线周期对齐）
	timestamps := generateTimestamps(startTime, endTime, interval)
	if len(timestamps) == 0 {
		return nil, fmt.Errorf("无效时间范围: startTime=%d, endTime=%d", startTime, endTime)
	}

	// 从 Redis 获取缓存的 K 线
	ctx := context.Background()
	var klines []*Kline
	missingTimestamps := make([]int64, 0)
	cacheHits := 0

	// 收集 HGet 命令
	hgetCmds := make([]*redis.StringCmd, 0, len(timestamps))
	err := dal.GetInstance().Redis().Pipelined(func(pipe redis.Pipeliner) error {
		for _, ts := range timestamps {
			cmd := pipe.HGet(ctx, redisKey, strconv.FormatInt(ts, 10))
			hgetCmds = append(hgetCmds, cmd)
		}
		return nil
	})
	if err != nil && err != redis.Nil {
		log.Printf("Redis 批量查询失败: %v", err)
	}

	// 处理 HGet 结果
	for i, ts := range timestamps {
		data, err := hgetCmds[i].Result()
		if err == nil {
			// 缓存命中
			var kline Kline
			if err := json.Unmarshal([]byte(data), &kline); err != nil {
				log.Printf("Redis 数据反序列化失败, timestamp=%d: %v", ts, err)
				missingTimestamps = append(missingTimestamps, ts)
			} else {
				klines = append(klines, &kline)
				cacheHits++
			}
		} else if err == redis.Nil {
			// 缓存缺失
			missingTimestamps = append(missingTimestamps, ts)
		} else {
			log.Printf("Redis 查询失败, timestamp=%d: %v", ts, err)
			missingTimestamps = append(missingTimestamps, ts)
		}
	}

	log.Printf("Redis 缓存命中: %d/%d K 线", cacheHits, len(timestamps))

	// 请求缺失的 K 线
	if len(missingTimestamps) > 0 {
		// 按时间范围分组请求
		missingKlines, err := fetchMissingKlines(client, symbol, interval, missingTimestamps)
		if err != nil {
			return nil, fmt.Errorf("获取缺失 K 线失败: %v", err)
		}
		klines = append(klines, missingKlines...)

		// 缓存新获取的 K 线
		err = dal.GetInstance().Redis().Pipelined(func(pipe redis.Pipeliner) error {
			for _, kline := range missingKlines {
				data, err := json.Marshal(kline)
				if err != nil {
					log.Printf("K 线序列化失败, timestamp=%d: %v", kline.Timestamp, err)
					continue
				}
				pipe.HSet(ctx, redisKey, kline.Timestamp, data)
			}
			// 设置 key 过期时间（30 天）
			pipe.Expire(ctx, redisKey, 30*24*time.Hour)
			return nil
		})
		if err != nil {
			log.Printf("Redis 存储 K 线失败: %v", err)
		} else {
			log.Printf("已缓存 %d 根新 K 线到 Redis: %s", len(missingKlines), redisKey)
		}
	}

	// 按时间戳排序
	sort.Slice(klines, func(i, j int) bool {
		return klines[i].Timestamp < klines[j].Timestamp
	})

	// 输出缓存命中率
	hitRate := float64(cacheHits) / float64(len(timestamps)) * 100
	fmt.Printf("Redis 缓存命中率: %.2f%%\n", hitRate)

	return klines, nil
}

// fetchMissingKlines 从币安 API 获取缺失的 K 线
func fetchMissingKlines(client *futures.Client, symbol, interval string, timestamps []int64) ([]*Kline, error) {
	var klines []*Kline
	limit := 1000

	// 按时间范围分组请求
	sort.Slice(timestamps, func(i, j int) bool { return timestamps[i] < timestamps[j] })
	ranges := groupTimestamps(timestamps, interval)
	for _, r := range ranges {
		startTime := r.start
		for startTime <= r.end {
			ks, err := client.NewKlinesService().
				Symbol(symbol).
				Interval(interval).
				StartTime(startTime).
				Limit(limit).
				Do(context.Background())
			if err != nil {
				return nil, fmt.Errorf("API 获取 K 线失败: %v", err)
			}

			for _, k := range ks {
				kline := &Kline{
					Open:      parseFloat(k.Open),
					High:      parseFloat(k.High),
					Low:       parseFloat(k.Low),
					Close:     parseFloat(k.Close),
					Volume:    parseFloat(k.Volume),
					Timestamp: k.OpenTime,
				}
				// 仅保留请求的时间戳
				for _, ts := range timestamps {
					if kline.Timestamp == ts {
						klines = append(klines, kline)
						break
					}
				}
			}

			if len(ks) == 0 {
				break
			}
			startTime = ks[len(ks)-1].CloseTime + 1
			if len(ks) < limit {
				break
			}
		}
	}

	return klines, nil
}

// generateTimestamps 生成 K 线时间戳列表
func generateTimestamps(startTime, endTime int64, interval string) []int64 {
	var timestamps []int64
	var intervalMs int64

	// 根据 interval 计算时间间隔（毫秒）
	switch interval {
	case "1m":
		intervalMs = 60 * 1000
	case "1h":
		intervalMs = 3600 * 1000
	case "4h":
		intervalMs = 4 * 3600 * 1000
	case "1d":
		intervalMs = 24 * 3600 * 1000
	default:
		log.Printf("不支持的 K 线周期: %s", interval)
		return nil
	}

	// 对齐 startTime 到 interval 边界
	startTime = (startTime / intervalMs) * intervalMs
	for ts := startTime; ts <= endTime; ts += intervalMs {
		timestamps = append(timestamps, ts)
	}

	return timestamps
}

// groupTimestamps 将时间戳分组为连续范围
type timeRange struct {
	start, end int64
}

func groupTimestamps(timestamps []int64, interval string) []timeRange {
	if len(timestamps) == 0 {
		return nil
	}

	var intervalMs int64
	switch interval {
	case "1m":
		intervalMs = 60 * 1000
	case "1h":
		intervalMs = 3600 * 1000
	case "4h":
		intervalMs = 4 * 3600 * 1000
	case "1d":
		intervalMs = 24 * 3600 * 1000
	default:
		return nil
	}

	var ranges []timeRange
	start := timestamps[0]
	last := start

	for i := 1; i < len(timestamps); i++ {
		if timestamps[i] == last+intervalMs {
			last = timestamps[i]
		} else {
			ranges = append(ranges, timeRange{start: start, end: last})
			start = timestamps[i]
			last = start
		}
	}
	ranges = append(ranges, timeRange{start: start, end: last})

	return ranges
}

// getHistoricalFundingRates 和 parseFloat 函数保持不变
func GetHistoricalFundingRates(client *futures.Client, symbol string, startTime, endTime int64) ([]*FundingRate, error) {
	var fundingRates []*FundingRate
	limit := 1000

	for startTime < endTime {
		fr, err := client.NewFundingRateService().
			Symbol(symbol).
			StartTime(startTime).
			Limit(limit).
			Do(context.Background())
		if err != nil {
			return nil, err
		}

		for _, f := range fr {
			fundingRates = append(fundingRates, &FundingRate{
				Symbol:    f.Symbol,
				Rate:      parseFloat(f.FundingRate),
				Timestamp: f.FundingTime,
			})
		}

		if len(fr) == 0 {
			break
		}
		startTime = fr[len(fr)-1].FundingTime + 1
		if len(fr) < limit {
			break
		}
	}

	return fundingRates, nil
}

func parseFloat(s string) float64 {
	f, _ := strconv.ParseFloat(s, 64)
	return f
}

// RunMartingale 运行马丁策略
func RunMartingale() {
	// 配置币安 API
	client := futures.NewClient(config.GlobalConfig.ApiKey, config.GlobalConfig.ApiSecret)

	// 设置回测参数
	symbol := "BTCUSDT"
	interval := "1h"
	startTime := time.Now().AddDate(0, -3, 0).Unix() * 1000 // 3 个月前
	endTime := time.Now().Unix() * 1000                     // 当前时间
	leverage := 10.0                                        // 杠杆倍数
	initialBalance := 10000.0                               // 初始资金 (USDT)
	feeRate := 0.04 / 100                                   // 交易手续费率 0.04%
	stopLossMultiplier := 1.5                               // ATR 止损倍数
	takeProfitMultiplier := 2.0                             // ATR 止盈倍数
	maintenanceMarginRate := 0.005                          // 维持保证金率 0.5%

	// 获取历史 K 线数据
	klines, err := GetHistoricalKlines(client, symbol, interval, startTime, endTime)
	if err != nil {
		log.Fatalf("获取 K 线数据失败: %v", err)
	}

	// 获取历史资金费率
	fundingRates, err := GetHistoricalFundingRates(client, symbol, startTime, endTime)
	if err != nil {
		log.Fatalf("获取资金费率失败: %v", err)
	}

	fmt.Println("=== 马丁策略回测 ===")

	// 运行马丁策略参数优化
	bestParams, bestBalance := OptimizeMartingaleParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	fmt.Printf("最佳马丁策略参数:\n")
	fmt.Printf("  初始仓位大小: %.2f USDT\n", bestParams.InitialPositionSize)
	fmt.Printf("  加仓倍数: %.2f\n", bestParams.Multiplier)
	fmt.Printf("  最大层数: %d\n", bestParams.MaxLevels)
	fmt.Printf("  止盈百分比: %.2f%%\n", bestParams.TakeProfitPercent*100)
	fmt.Printf("  最大亏损百分比: %.2f%%\n", bestParams.MaxLossPercent*100)
	fmt.Printf("  网格间距: %.2f%%\n", bestParams.GridSpacing*100)
	fmt.Printf("  使用ATR间距: %t\n", bestParams.UseATRSpacing)
	if bestParams.UseATRSpacing {
		fmt.Printf("  ATR周期: %d\n", bestParams.ATRPeriod)
		fmt.Printf("  ATR倍数: %.2f\n", bestParams.ATRMultiplier)
	}
	fmt.Printf("最佳资金: %.2f USDT (收益率: %.2f%%)\n", bestBalance, (bestBalance-initialBalance)/initialBalance*100)

	// 使用最佳参数运行回测
	bt := NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
		return MartingaleStrategy(klines, bt, index, bestParams)
	})

	// 输出详细结果
	fmt.Printf("\n=== 马丁策略回测结果 ===\n")
	fmt.Printf("最终资金: %.2f USDT\n", bt.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt.TradeCount)
	if bt.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt.WinCount)/float64(bt.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt.MaxDrawdown*100)
	fmt.Printf("止损触发次数: %d\n", bt.StopLossCount)
	fmt.Printf("止盈触发次数: %d\n", bt.TakeProfitCount)
	fmt.Printf("清算次数: %d\n", bt.LiquidationCount)
	fmt.Printf("最大马丁层数: %d\n", len(bt.MartingaleLevels))
}

// RunTrendFollow 运行趋势跟随策略
func RunTrendFollow() {
	// 配置币安 API
	client := futures.NewClient(config.GlobalConfig.ApiKey, config.GlobalConfig.ApiSecret)

	// 设置回测参数
	symbol := "BTCUSDT"
	interval := "1h"                                         // 推荐使用1小时线
	startTime := time.Now().AddDate(0, -3, 0).Unix() * 1000 // 3 个月前
	endTime := time.Now().Unix() * 1000                     // 当前时间
	leverage := 2.0                                          // 趋势跟随策略建议使用1-2倍杠杆
	initialBalance := 10000.0                               // 初始资金 (USDT)
	feeRate := 0.04 / 100                                   // 交易手续费率 0.04%
	stopLossMultiplier := 1.5                               // ATR 止损倍数
	takeProfitMultiplier := 2.0                             // ATR 止盈倍数
	maintenanceMarginRate := 0.005                          // 维持保证金率 0.5%

	// 获取历史 K 线数据
	klines, err := GetHistoricalKlines(client, symbol, interval, startTime, endTime)
	if err != nil {
		log.Fatalf("获取 K 线数据失败: %v", err)
	}

	// 获取历史资金费率
	fundingRates, err := GetHistoricalFundingRates(client, symbol, startTime, endTime)
	if err != nil {
		log.Fatalf("获取资金费率失败: %v", err)
	}

	fmt.Println("=== 趋势跟随+回调加仓策略回测 ===")

	// 运行趋势跟随策略参数优化
	bestParams, bestBalance := OptimizeTrendFollowParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	fmt.Printf("最佳趋势跟随策略参数:\n")
	fmt.Printf("  EMA快线: %d\n", bestParams.EMAFast)
	fmt.Printf("  EMA慢线: %d\n", bestParams.EMASlow)
	fmt.Printf("  初始仓位比例: %.1f%%\n", bestParams.InitialPositionRatio*100)
	fmt.Printf("  回调加仓触发: %.1f%%\n", bestParams.CallbackPercent*100)
	fmt.Printf("  最大加仓次数: %d次\n", bestParams.MaxAddPositions)
	fmt.Printf("  最大持仓比例: %.1f%%\n", bestParams.MaxPositionRatio*100)
	fmt.Printf("  止盈点: %.1f%%\n", bestParams.TakeProfitPercent*100)
	fmt.Printf("  止损点: %.1f%%\n", bestParams.StopLossPercent*100)
	fmt.Printf("  RSI范围: %.0f-%.0f (出场>%.0f)\n", bestParams.RSILowerBound, bestParams.RSIUpperBound, bestParams.RSIExitBound)
	fmt.Printf("最佳资金: %.2f USDT (收益率: %.2f%%)\n", bestBalance, (bestBalance-initialBalance)/initialBalance*100)

	// 使用最佳参数运行回测
	bt := NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt.Run(klines, fundingRates, func(klines []*Kline, bt *Backtest, index int) (TradeSignal, float64) {
		return TrendFollowStrategy(klines, bt, index, bestParams)
	})

	// 输出详细结果
	fmt.Printf("\n=== 趋势跟随策略回测结果 ===\n")
	fmt.Printf("最终资金: %.2f USDT\n", bt.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt.TradeCount)
	if bt.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt.WinCount)/float64(bt.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt.MaxDrawdown*100)
	fmt.Printf("止损触发次数: %d\n", bt.StopLossCount)
	fmt.Printf("止盈触发次数: %d\n", bt.TakeProfitCount)
	fmt.Printf("最大持仓层数: %d\n", len(bt.TrendFollowPositions))

	// 验证回测指标
	fmt.Printf("\n=== 策略验证指标 ===\n")
	winRate := 0.0
	if bt.TradeCount > 0 {
		winRate = float64(bt.WinCount) / float64(bt.TradeCount) * 100
	}
	totalReturn := (bt.Balance - initialBalance) / initialBalance * 100
	maxDrawdown := bt.MaxDrawdown * 100

	fmt.Printf("胜率: %.1f%% (目标: >50%%)\n", winRate)
	fmt.Printf("年化收益率: %.1f%% (目标: >20%%)\n", totalReturn*4) // 3个月数据，简单年化
	fmt.Printf("最大回撤: %.1f%% (目标: <10%%)\n", maxDrawdown)

	// 评估策略表现
	if winRate > 50 && totalReturn*4 > 20 && maxDrawdown < 10 {
		fmt.Println("✅ 策略表现优秀，符合预期指标")
	} else {
		fmt.Println("⚠️  策略表现需要优化")
		if winRate <= 50 {
			fmt.Printf("   - 胜率偏低: %.1f%% (需要>50%%)\n", winRate)
		}
		if totalReturn*4 <= 20 {
			fmt.Printf("   - 年化收益偏低: %.1f%% (需要>20%%)\n", totalReturn*4)
		}
		if maxDrawdown >= 10 {
			fmt.Printf("   - 最大回撤过高: %.1f%% (需要<10%%)\n", maxDrawdown)
		}
	}
}

// RunBreakthrough 运行双向突破策略
func RunBreakthrough() {
	// 配置币安 API
	client := futures.NewClient(config.GlobalConfig.ApiKey, config.GlobalConfig.ApiSecret)

	// 设置回测参数
	symbols := []string{"BTCUSDT", "ETHUSDT", "SOLUSDT", "BNBUSDT"} // 支持的币种
	interval := "5m"                                               // 推荐使用5分钟线
	startTime := time.Now().AddDate(0, 0, -7).Unix() * 1000       // 7天前
	endTime := time.Now().Unix() * 1000                           // 当前时间
	leverage := 20.0                                               // 双向突破策略默认20倍杠杆
	initialBalance := 500.0                                        // 初始资金500U
	feeRate := 0.04 / 100                                          // 交易手续费率 0.04%
	stopLossMultiplier := 1.5                                      // ATR 止损倍数
	takeProfitMultiplier := 2.0                                    // ATR 止盈倍数
	maintenanceMarginRate := 0.005                                 // 维持保证金率 0.5%

	// 默认使用BTC数据进行回测
	symbol := symbols[0]

	// 获取历史 K 线数据
	klines, err := GetHistoricalKlines(client, symbol, interval, startTime, endTime)
	if err != nil {
		log.Fatalf("获取 K 线数据失败: %v", err)
	}

	// 获取历史资金费率
	fundingRates, err := GetHistoricalFundingRates(client, symbol, startTime, endTime)
	if err != nil {
		log.Fatalf("获取资金费率失败: %v", err)
	}

	fmt.Println("=== 双向突破+高频止盈策略回测 ===")

	// 运行双向突破策略参数优化
	bestParams, bestBalance := OptimizeBreakthroughParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	fmt.Printf("最佳双向突破策略参数:\n")
	fmt.Printf("  EMA快线: %d, EMA慢线: %d\n", bestParams.EMAFast, bestParams.EMASlow)
	fmt.Printf("  布林带周期: %d, 标准差: %.1f\n", bestParams.BollingerPeriod, bestParams.BollingerStdDev)
	fmt.Printf("  突破阈值: %.2f%%\n", bestParams.BreakoutThreshold*100)
	fmt.Printf("  止盈范围: %.2f%% - %.2f%%\n", bestParams.TakeProfitMin*100, bestParams.TakeProfitMax*100)
	fmt.Printf("  止损范围: %.2f%% - %.2f%%\n", bestParams.StopLossMin*100, bestParams.StopLossMax*100)
	fmt.Printf("  仓位大小: %.0fU\n", bestParams.PositionSize)
	fmt.Printf("  杠杆范围: %.0fx - %.0fx\n", bestParams.LeverageDefault, bestParams.LeverageMax)
	fmt.Printf("  最大持仓: %d个\n", bestParams.MaxPositions)
	fmt.Printf("  每日目标: %.0fU\n", bestParams.DailyTarget)
	fmt.Printf("  最大回撤: %.0fU\n", bestParams.MaxDrawdown)
	fmt.Printf("最佳资金: %.2f USDT (收益率: %.2f%%)\n", bestBalance, (bestBalance-initialBalance)/initialBalance*100)

	// 使用最佳参数运行回测
	bt := NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt.RunBreakthrough(klines, fundingRates, bestParams, func(klines []*Kline, bt *Backtest, index int, params BreakthroughParams, symbol string, allSymbolPrices map[string]float64) (TradeSignal, float64, string) {
		return BreakthroughStrategy(klines, bt, index, params, symbol, allSymbolPrices)
	})

	// 输出详细结果
	fmt.Printf("\n=== 双向突破策略回测结果 ===\n")
	fmt.Printf("最终资金: %.2f USDT\n", bt.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt.TradeCount)
	if bt.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt.WinCount)/float64(bt.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt.MaxDrawdown*100)
	fmt.Printf("当日盈亏: %.2f USDT\n", bt.BreakthroughDailyPnL)
	fmt.Printf("总盈亏: %.2f USDT\n", bt.BreakthroughTotalPnL)
	fmt.Printf("活跃持仓: %d个\n", len(bt.GetActiveBreakthroughPositions()))
	fmt.Printf("总持仓记录: %d个\n", len(bt.BreakthroughPositions))

	// 显示持仓详情
	activePositions := bt.GetActiveBreakthroughPositions()
	if len(activePositions) > 0 {
		fmt.Printf("\n=== 当前活跃持仓 ===\n")
		for _, pos := range activePositions {
			fmt.Printf("ID: %d, %s %s, 入场价: %.2f, 仓位: %.2f, 杠杆: %.0fx\n",
				pos.ID, pos.Symbol, pos.Direction, pos.EntryPrice, pos.Size, pos.Leverage)
			fmt.Printf("  止盈: %.2f, 止损: %.2f\n", pos.TakeProfit, pos.StopLoss)
		}
	}

	// 验证策略指标
	fmt.Printf("\n=== 策略验证指标 ===\n")
	winRate := 0.0
	if bt.TradeCount > 0 {
		winRate = float64(bt.WinCount) / float64(bt.TradeCount) * 100
	}
	totalReturn := (bt.Balance - initialBalance) / initialBalance * 100
	maxDrawdown := bt.MaxDrawdown * 100
	dailyTargetAchieved := bt.BreakthroughDailyPnL >= bestParams.DailyTarget

	fmt.Printf("胜率: %.1f%% (目标: >55%%)\n", winRate)
	fmt.Printf("总收益率: %.1f%%\n", totalReturn)
	fmt.Printf("最大回撤: %.1f%% (限制: <%.0f%%)\n", maxDrawdown, bestParams.MaxDrawdown/initialBalance*100)
	fmt.Printf("每日目标达成: %v (目标: %.0fU, 实际: %.2fU)\n", dailyTargetAchieved, bestParams.DailyTarget, bt.BreakthroughDailyPnL)

	// 评估策略表现
	if winRate > 55 && totalReturn > 0 && maxDrawdown < bestParams.MaxDrawdown/initialBalance*100 {
		fmt.Println("✅ 策略表现优秀，符合预期指标")
		if dailyTargetAchieved {
			fmt.Println("🎯 已达成每日收益目标！")
		}
	} else {
		fmt.Println("⚠️  策略表现需要优化")
		if winRate <= 55 {
			fmt.Printf("   - 胜率偏低: %.1f%% (需要>55%%)\n", winRate)
		}
		if totalReturn <= 0 {
			fmt.Printf("   - 总收益为负: %.1f%%\n", totalReturn)
		}
		if maxDrawdown >= bestParams.MaxDrawdown/initialBalance*100 {
			fmt.Printf("   - 最大回撤过高: %.1f%%\n", maxDrawdown)
		}
	}

	// 币种轮动建议
	fmt.Printf("\n=== 币种轮动建议 ===\n")
	fmt.Printf("支持币种: %v\n", symbols)
	fmt.Printf("当前测试币种: %s\n", symbol)
	fmt.Printf("建议: 实盘运行时应实时监控各币种动量，选择最活跃的币种交易\n")
}
