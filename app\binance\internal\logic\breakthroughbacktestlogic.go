package logic

import (
	"context"
	"fmt"

	"zblockchain/app/binance/config"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"
	binanceLogic "zblockchain/app/binance/logic"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/logx"
)

type BreakthroughBacktestLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBreakthroughBacktestLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BreakthroughBacktestLogic {
	return &BreakthroughBacktestLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BreakthroughBacktestLogic) BreakthroughBacktest(req *types.BreakthroughBacktestRequest) (resp *types.BreakthroughBacktestResponse, err error) {
	// 配置币安 API
	client := futures.NewClient(config.GlobalConfig.ApiKey, config.GlobalConfig.ApiSecret)

	// 设置回测参数
	leverage := 20.0 // 双向突破策略默认20倍杠杆
	initialBalance := 500.0 // 初始资金500U
	feeRate := 0.04 / 100
	stopLossMultiplier := 1.5
	takeProfitMultiplier := 2.0
	maintenanceMarginRate := 0.005

	// 默认使用BTC数据，实际应该支持多币种
	symbol := "BTCUSDT"
	if len(req.Symbols) > 0 {
		symbol = req.Symbols[0]
	}

	// 获取历史 K 线数据
	klines, err := binanceLogic.GetHistoricalKlines(client, symbol, req.Interval, req.StartTime, req.EndTime)
	if err != nil {
		return &types.BreakthroughBacktestResponse{
			Success: false,
			Message: fmt.Sprintf("获取 K 线数据失败: %v", err),
		}, nil
	}

	// 获取历史资金费率
	fundingRates, err := binanceLogic.GetHistoricalFundingRates(client, symbol, req.StartTime, req.EndTime)
	if err != nil {
		return &types.BreakthroughBacktestResponse{
			Success: false,
			Message: fmt.Sprintf("获取资金费率失败: %v", err),
		}, nil
	}

	var bestParams binanceLogic.BreakthroughParams

	if req.UseOptimize {
		// 运行参数优化
		bestParams, _ = binanceLogic.OptimizeBreakthroughParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	} else {
		// 使用默认参数
		bestParams = binanceLogic.GetDefaultBreakthroughParams()
	}

	// 使用最佳参数运行回测
	bt := binanceLogic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt.RunBreakthrough(klines, fundingRates, bestParams, func(klines []*binanceLogic.Kline, bt *binanceLogic.Backtest, index int, params binanceLogic.BreakthroughParams, symbol string, allSymbolPrices map[string]float64) (binanceLogic.TradeSignal, float64, string) {
		return binanceLogic.BreakthroughStrategy(klines, bt, index, params, symbol, allSymbolPrices)
	})

	// 转换双向突破持仓数据
	positions := make([]types.BreakthroughPosition, len(bt.BreakthroughPositions))
	for i, pos := range bt.BreakthroughPositions {
		positions[i] = types.BreakthroughPosition{
			ID:         pos.ID,
			Symbol:     pos.Symbol,
			Direction:  pos.Direction,
			EntryPrice: pos.EntryPrice,
			Size:       pos.Size,
			Leverage:   pos.Leverage,
			TakeProfit: pos.TakeProfit,
			StopLoss:   pos.StopLoss,
			Timestamp:  pos.Timestamp,
			IsActive:   pos.IsActive,
		}
	}

	// 计算活跃持仓数
	activePositions := 0
	for _, pos := range positions {
		if pos.IsActive {
			activePositions++
		}
	}

	// 计算胜率
	winRate := 0.0
	if bt.TradeCount > 0 {
		winRate = float64(bt.WinCount) / float64(bt.TradeCount) * 100
	}

	return &types.BreakthroughBacktestResponse{
		Success:         true,
		Message:         "双向突破策略回测完成",
		InitialBalance:  initialBalance,
		FinalBalance:    bt.Balance,
		TotalReturn:     (bt.Balance - initialBalance) / initialBalance * 100,
		TradeCount:      bt.TradeCount,
		WinRate:         winRate,
		MaxDrawdown:     bt.MaxDrawdown * 100,
		DailyPnL:        bt.BreakthroughDailyPnL,
		TotalPnL:        bt.BreakthroughTotalPnL,
		BestParams: types.BreakthroughConfigRequest{
			EMAFast:           bestParams.EMAFast,
			EMASlow:           bestParams.EMASlow,
			BollingerPeriod:   bestParams.BollingerPeriod,
			BollingerStdDev:   bestParams.BollingerStdDev,
			BreakoutThreshold: bestParams.BreakoutThreshold,
			TakeProfitMin:     bestParams.TakeProfitMin,
			TakeProfitMax:     bestParams.TakeProfitMax,
			StopLossMin:       bestParams.StopLossMin,
			StopLossMax:       bestParams.StopLossMax,
			PositionSize:      bestParams.PositionSize,
			LeverageDefault:   bestParams.LeverageDefault,
			LeverageMax:       bestParams.LeverageMax,
			MaxPositions:      bestParams.MaxPositions,
			DailyTarget:       bestParams.DailyTarget,
			MaxDrawdown:       bestParams.MaxDrawdown,
			VolumeMultiplier:  bestParams.VolumeMultiplier,
			VolumePeriod:      bestParams.VolumePeriod,
			MomentumPeriod:    bestParams.MomentumPeriod,
			InitialBalance:    bestParams.InitialBalance,
		},
		Positions:       positions,
		ActivePositions: activePositions,
	}, nil
}
