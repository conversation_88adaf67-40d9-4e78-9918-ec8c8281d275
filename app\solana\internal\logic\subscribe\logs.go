package subscribe

import (
	"context"
	"zblockchain/app/solana/internal/svc"
	"zblockchain/common/routine"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/zeromicro/go-zero/core/logx"
)

type LogsSubscribe struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLogsSubscribe(ctx context.Context, svcCtx *svc.ServiceContext) *LogsSubscribe {
	return &LogsSubscribe{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *LogsSubscribe) AddLogsSubscribe(pubKey string) error {
	program := solana.MustPublicKeyFromBase58(pubKey) // serum
	return a.subscribe(program)
}

func (a *LogsSubscribe) subscribe(program solana.PublicKey) error{
	err := routine.Run(true, func() {
		sub, err := a.svcCtx.QN_ws_clinet.LogsSubscribeMentions(
			program,
			rpc.CommitmentRecent,
		)

		if err != nil {
			panic(err)
		  }
		  defer sub.Unsubscribe()
	  
		  for {
			got, err := sub.Recv(a.ctx)
			if err != nil {
			  panic(err)
			}
			logx.Debugf("LogsSubscribe Signature:%v", got.Value.Signature)
			
			logx.Debugf("LogsSubscribe Logs:%v", got.Value.Logs)
		  }
	})
	return err
}
