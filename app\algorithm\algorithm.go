package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"zblockchain/app/algorithm/internal/agent"
	"zblockchain/app/algorithm/internal/config"
	"zblockchain/app/algorithm/internal/server"
	"zblockchain/app/algorithm/internal/svc"
	"zblockchain/app/algorithm/pb"
	"zblockchain/common/network"
	"zblockchain/common/network/core"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/algorithm.yaml", "the config file")

func loadMathConfig(path string) ([]config.MathConfig, error) {
	content, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var addresses []config.MathConfig
	err = json.Unmarshal(content, &addresses)
	if err != nil {
		log.Fatalf("Error unmarshaling JSON: %v", err)
	}
	return addresses, err
}

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	ctx := svc.NewServiceContext(c)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		pb.RegisterAlgorithmServer(grpcServer, server.NewAlgorithmServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()
	addresses, err := loadMathConfig(c.CommonConfig.MathConfigDir)
	if err != nil {
		panic(err)
	}
	for i := 0; i < len(addresses); i++ {
		_, err = network.Connect(network.TcpNet, fmt.Sprintf("%s:%d", addresses[i].Ip, addresses[i].Port), func() core.Agent {
			return agent.NewAgent(addresses[i].GameId)
		}, core.WithReconnect(true))
		if err != nil {
			panic(err)
		}
	}
	fmt.Printf("Starting algorithm server at %s..., Clinet:%d\n", c.ListenOn, network.GetConnNum())
	s.Start()
}
