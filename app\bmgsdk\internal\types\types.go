// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.3

package types

type LoginReq struct {
	Token  string `json:"token"`
	OpenId string `json:"openId"`
}

type LoginResp struct {
	Code     int64    `json:"code"`
	Message  string   `json:"message"`
	UserInfo UserInfo `json:"data"`
	VipInfo  []*Vip   `json:"vip"`
}

type PayCallbackReq struct {
	OpenId       string `json:"openId"`
	OrderId      string `json:"orderId"`
	ProductId    string `json:"productId"`
	PayTime      int64  `json:"payTime"`
	Price        int64  `json:"price"`
	ExtrasParams string `json:"extrasParams"`
	Sign         string `json:"sign"`
}

type PayCallbackRsp struct {
	Ret int64  `json:"ret"`
	Msg string `json:"msg"`
}

type PayResultReq struct {
	OrderId string `json:"orderId"`
	OpenId  string `json:"openId"`
}

type PayResultRsp struct {
	Code    int64  `json:"code"`
	VipInfo []*Vip `json:"vip"`
}

type UserInfo struct {
	Nickname string `json:"nickName"`
	PicUrl   string `json:"picUrl"`
	Sex      int64  `json:"sex"`
	Brithday string `json:"brithday"`
}

type Vip struct {
	Id   int64 `json:"id"`
	Time int64 `json:"time"`
}
