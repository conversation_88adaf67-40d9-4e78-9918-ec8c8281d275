global:
  scrape_interval:
  external_labels:
    monitor: 'codelab-monitor'

scrape_configs:
  - job_name: 'prometheus'
    scrape_interval: 5s  #global catch time
    static_configs:
      - targets: ['127.0.0.1:9090']
  - job_name: 'usercenter-api'
    static_configs:
      - targets: ['zgame:4008']
        labels:
          job: usercenter-api
          app: usercenter-api
          env: dev
  - job_name: 'usercenter-rpc'
    static_configs:
      - targets: ['zgame:4009']
        labels:
          job: usercenter-rpc
          app: usercenter-rpc
          env: dev

