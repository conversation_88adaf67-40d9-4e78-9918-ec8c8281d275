package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VipModel = (*customVipModel)(nil)

type (
	// VipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVipModel.
	VipModel interface {
		vipModel
		RowBuilder() squirrel.SelectBuilder
		FindByQuery(ctx context.Context, rowBuilder squirrel.SelectBuilder) ([]*Vip, error)
		withSession(session sqlx.Session) VipModel
	}

	customVipModel struct {
		*defaultVipModel
	}
)

// NewVipModel returns a model for the database table.
func NewVipModel(conn sqlx.SqlConn) VipModel {
	return &customVipModel{
		defaultVipModel: newVipModel(conn),
	}
}

func (m *customVipModel) withSession(session sqlx.Session) VipModel {
	return NewVipModel(sqlx.NewSqlConnFromSession(session))
}

func (m *defaultVipModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vipRows).From(m.table)
}

func (m *defaultVipModel) FindByQuery(ctx context.Context, rowBuilder squirrel.SelectBuilder) ([]*Vip, error) {

	query, values, err := rowBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Vip
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
