package main

import (
	"context"
	"fmt"
	"strings"

	"zblockchain/app/binance/internal/config"
	"zblockchain/app/binance/internal/logic"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/conf"
)

func mainTestnet() {
	// 加载配置
	var c config.Config
	conf.MustLoad("etc/binance.yaml", &c)

	fmt.Println("=== 测试网配置验证 ===")
	fmt.Printf("配置的HTTP URL: %s\n", c.BaseUrl.Http)
	
	// 测试直接使用futures.NewClient
	fmt.Println("\n=== 测试1: 直接使用futures.NewClient ===")
	client1 := futures.NewClient(c.<PERSON>, c.ApiSecret)
	fmt.Printf("默认BaseURL: %s\n", client1.BaseURL)
	
	// 测试我们的逻辑修复
	fmt.Println("\n=== 测试2: 使用我们的逻辑修复 ===")
	client2 := futures.NewClient(c.<PERSON>, c.ApiSecret)
	
	// 应用我们的修复逻辑
	if strings.Contains(c.BaseUrl.Http, "testnet") {
		client2.BaseURL = "https://testnet.binancefuture.com"
		fmt.Printf("修复后BaseURL: %s ✅\n", client2.BaseURL)
	} else {
		client2.BaseURL = c.BaseUrl.Http
		fmt.Printf("生产环境BaseURL: %s\n", client2.BaseURL)
	}
	
	// 测试GetKlines逻辑
	fmt.Println("\n=== 测试3: GetKlines API调用 ===")
	svcCtx := svc.NewServiceContext(c)
	ctx := context.Background()
	l := logic.NewGetKlinesLogic(ctx, svcCtx)

	req := &types.KlineRequest{
		Symbol:   "BTCUSDT",
		Interval: "1h",
		Limit:    1,
	}

	fmt.Printf("请求参数: Symbol=%s, Interval=%s, Limit=%d\n", 
		req.Symbol, req.Interval, req.Limit)
	
	resp, err := l.GetKlines(req)
	if err != nil {
		fmt.Printf("❌ API调用失败: %v\n", err)
		
		// 检查错误类型
		if strings.Contains(err.Error(), "testnet.binance.vision") {
			fmt.Println("🔍 错误分析: 仍在使用现货测试网URL，需要使用期货测试网URL")
		} else if strings.Contains(err.Error(), "fapi.binance.com") {
			fmt.Println("🔍 错误分析: 正在使用生产网URL")
		} else if strings.Contains(err.Error(), "testnet.binancefuture.com") {
			fmt.Println("✅ 错误分析: 正确使用期货测试网URL，可能是网络或认证问题")
		}
	} else {
		fmt.Println("✅ API调用成功")
		if len(resp.Data) > 0 {
			kline := resp.Data[0]
			fmt.Printf("✅ 获取到K线数据: 开盘价=%.2f, 收盘价=%.2f\n", 
				kline.Open, kline.Close)
		}
	}
	
	// 环境建议
	fmt.Println("\n=== 环境状态总结 ===")
	if strings.Contains(c.BaseUrl.Http, "testnet") {
		fmt.Println("✅ 当前使用测试网环境")
		fmt.Println("✅ 资金安全: 使用虚拟资金")
		fmt.Println("✅ 数据真实: K线数据为真实市场数据")
		
		if strings.Contains(c.BaseUrl.Http, "binancefuture.com") {
			fmt.Println("✅ URL配置: 正确使用期货测试网URL")
		} else {
			fmt.Println("🔧 URL配置: 代码会自动转换为期货测试网URL")
		}
	} else {
		fmt.Println("⚠️  当前使用生产网环境")
		fmt.Println("⚠️  请确保您了解真实资金风险")
	}
	
	fmt.Println("\n=== 修复说明 ===")
	fmt.Println("1. 币安期货API和现货API使用不同的域名")
	fmt.Println("2. 期货测试网: https://testnet.binancefuture.com")
	fmt.Println("3. 现货测试网: https://testnet.binance.vision")
	fmt.Println("4. 我们的代码已自动处理URL转换")
}
