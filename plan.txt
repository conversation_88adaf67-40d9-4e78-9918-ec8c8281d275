策略名称：趋势跟随 + 回调加仓（小资金 BTC 做多策略）

一、策略逻辑（核心思路）

1. 判断多头趋势：

   * 使用 EMA5 和 EMA20 判断是否处于上涨趋势：

     * EMA5 > EMA20 表示趋势为多头，可以考虑入场做多。

2. 入场条件：

   * 当前价格大于 EMA20；
   * 且 K线没有长上影线，当前 RSI 在 40\~70 之间；
   * 成交量大于过去5个周期平均成交量。

3. 建仓和加仓逻辑：

   * 初始开仓：仓位占比 20%；
   * 如果回调 2%，且趋势不变（EMA5 仍 > EMA20），再加一笔（最多加仓 2 次）；
   * 每次加仓仓位再投入 20%，最高 60% 仓位；
   * 剩余40%资金为风险准备金（止损/拉平使用）。

4. 止盈止损逻辑：

   * 止损：跌破 EMA20 超过 1.5%；
   * 止盈：盈利达到 6% 时分批止盈（卖出 50%，剩下挂尾单）；
   * 或当前 RSI > 80 并出现长上影K线，提前止盈。

5. 资金管理：

   * 每笔下单 USDT 金额 = 当前账户余额 \* 仓位比例；
   * 设置杠杆（建议 1\~2 倍，控制风险）。

二、参数设置建议（可调整）

| 参数     | 数值               | 说明         |
| ------ | ---------------- | ---------- |
| EMA 快线 | 5                | 短期趋势线      |
| EMA 慢线 | 20               | 趋势判断依据     |
| 初始仓位   | 20%              | 第一笔资金占比    |
| 加仓触发回调 | 2%               | 从前一单入场价开始  |
| 加仓次数   | 2 次              | 最多 3 笔持仓   |
| 最大持仓   | 60%              | 其余资金为风险准备金 |
| 止盈点    | 6%               | 分批止盈，保收益   |
| 止损点    | -1.5%            | 强行出场防止大亏   |
| RSI 范围 | 40\~70 入场，>80 出场 |            |
| 时间周期   | 15分钟 或 1小时线      | 推荐使用 1小时线  |

三、程序实现模块建议
\[数据源模块]
→ 实时拉取BTC价格、K线、EMA、RSI、成交量等数据

\[信号生成模块]
→ 判断趋势、多头信号、加仓信号、止盈止损信号

\[风控模块]
→ 控制最大仓位，平衡资金使用，设置强平阈值

\[下单模块]
→ 自动调用交易所API进行挂单、撤单、加仓、平仓等操作

\[日志模块]
→ 记录每次操作、盈亏、参数变化等，便于回测和优化


五、回测建议
在上线前请用过去的 BTC 数据做回测，验证：

* 胜率是否 > 50%
* 单笔最大亏损控制在 2% 以内
* 最大回撤 < 10%
* 年化收益率 > 20%

策略名称：双向突破 + 高频止盈策略（适用于 BTC/ETH/SOL/BNB）

一、策略目标

初始资金：500U

每日收益目标：100U

最大允许回撤：100U

可使用杠杆：最高 100x

支持方向：双向（做多 + 做空）

可交易币种：BTC、ETH、SOL、BNB

二、核心策略逻辑

突破识别：

使用布林带 + EMA 判断行情是否存在突破；

上轨突破且 EMA5 > EMA20 → 做多信号；

下轨突破且 EMA5 < EMA20 → 做空信号；

成交量需较前5根K线均值放大 > 1.2倍，确认有效突破。

币种轮动选择：

实时比较 BTC、ETH、SOL、BNB 的动能：

最近10根K线涨幅/跌幅排名；

成交量变化排名；

选出当前最活跃的币种作为操作对象。

高频止盈止损：

每笔目标止盈：+1% ~ +1.5%；

每笔止损限制：-0.5% ~ -0.8%；

若当前浮盈达到每日目标（100U），则清仓并停止策略运行（冷静期 24h）。

仓位和杠杆：

每次进场仓位本金：50U

杠杆：默认 20x，若突破强烈（布林张口 + 强成交量）可提至 50x

全部仓位最大同时持仓金额 ≈ 3 笔（分散风险）

回撤控制机制：

实时追踪当前账户净值；

若当前累计亏损 ≥ 100U，立即强制平所有仓位，休眠24小时。

三、技术指标参数

指标

设置

EMA 快线

5

EMA 慢线

20

布林带周期

20

布林宽度判断

上/下轨突破0.5%

止盈点位

+1.0% ~ +1.5%

止损点位

-0.5% ~ -0.8%

杠杆调整

20x~50x

动量周期

最近10根K线

四、程序模块建议

行情模块：拉取实时K线/价格/指标（建议使用 ccxt + WebSocket）

动能分析模块：动量排序 + 成交量排名，选出目标币

信号模块：结合 BOLL + EMA 判断方向并生成开仓信号

下单模块：计算杠杆、止盈/止损价格，挂单或市价单

止盈止损模块：浮盈/浮亏达阈值立即触发

收益追踪模块：当日净利润 ≥ 100U 自动停止运行

风控模块：净值回撤 ≥ 100U，立即全平仓并禁用策略一天

日志模块：记录所有交易行为、盈亏数据、币种切换等

五、实战建议

推荐使用 1分钟 或 5分钟线，更容易找到短期突破；

回测胜率 ≥ 55% 才可上线实盘运行；

设定 API 权限时限制最大下单金额/数量，避免误操作；

可拓展 Telegram 报警，动态通知当前状态和盈亏；
