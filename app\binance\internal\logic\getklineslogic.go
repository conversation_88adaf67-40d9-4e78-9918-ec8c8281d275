package logic

import (
	"context"
	"math"
	"math/rand"
	"time"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetKlinesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetKlinesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetKlinesLogic {
	return &GetKlinesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetKlinesLogic) GetKlines(req *types.KlineRequest) (resp *types.KlineResponse, err error) {
	// 设置默认值
	if req.Limit == 0 {
		req.Limit = 100
	}
	if req.Interval == "" {
		req.Interval = "1h"
	}

	// 生成模拟K线数据
	klines := l.generateMockKlines(req.Symbol, req.Interval, req.Limit)

	return &types.KlineResponse{
		Symbol: req.Symbol,
		Data:   klines,
	}, nil
}

func (l *GetKlinesLogic) generateMockKlines(symbol, interval string, limit int) []types.KlineData {
	// 获取基础价格
	basePrice := l.getBasePrice(symbol)
	
	// 计算时间间隔（毫秒）
	intervalMs := l.getIntervalMs(interval)
	
	// 生成K线数据
	klines := make([]types.KlineData, limit)
	currentTime := time.Now().Unix()*1000 - int64(limit)*intervalMs
	currentPrice := basePrice
	
	for i := 0; i < limit; i++ {
		// 生成价格波动
		change := (rand.Float64() - 0.5) * 0.02 // ±1%的波动
		newPrice := currentPrice * (1 + change)
		
		// 生成OHLC
		open := currentPrice
		close := newPrice
		high := math.Max(open, close) * (1 + rand.Float64()*0.005) // 最高价
		low := math.Min(open, close) * (1 - rand.Float64()*0.005)  // 最低价
		volume := 1000 + rand.Float64()*9000 // 随机成交量
		
		klines[i] = types.KlineData{
			OpenTime:  currentTime,
			Open:      open,
			High:      high,
			Low:       low,
			Close:     close,
			Volume:    volume,
			CloseTime: currentTime + intervalMs - 1,
		}
		
		currentTime += intervalMs
		currentPrice = newPrice
	}
	
	return klines
}

func (l *GetKlinesLogic) getBasePrice(symbol string) float64 {
	switch symbol {
	case "BTCUSDT":
		return 43000.0
	case "ETHUSDT":
		return 2600.0
	case "BNBUSDT":
		return 320.0
	case "ADAUSDT":
		return 0.45
	case "SOLUSDT":
		return 95.0
	default:
		return 100.0
	}
}

func (l *GetKlinesLogic) getIntervalMs(interval string) int64 {
	switch interval {
	case "1m":
		return 60 * 1000
	case "5m":
		return 5 * 60 * 1000
	case "15m":
		return 15 * 60 * 1000
	case "1h":
		return 60 * 60 * 1000
	case "4h":
		return 4 * 60 * 60 * 1000
	case "1d":
		return 24 * 60 * 60 * 1000
	default:
		return 60 * 60 * 1000 // 默认1小时
	}
}
