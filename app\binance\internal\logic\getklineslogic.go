package logic

import (
	"context"
	"fmt"
	"strconv"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetKlinesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetKlinesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetKlinesLogic {
	return &GetKlinesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetKlinesLogic) GetKlines(req *types.KlineRequest) (resp *types.KlineResponse, err error) {
	// 设置默认值
	if req.Limit == 0 {
		req.Limit = 100
	}
	if req.Interval == "" {
		req.Interval = "1h"
	}

	// 创建币安期货客户端
	client := futures.NewClient(l.svcCtx.Config.ApiKey, l.svcCtx.Config.ApiSecret)
	client.BaseURL = l.svcCtx.Config.BaseUrl.DataUrl

	// 构建K线请求
	klineService := client.NewKlinesService().
		Symbol(req.Symbol).
		Interval(req.Interval).
		Limit(req.Limit)

	// 如果指定了开始时间，添加到请求中
	if req.StartTime > 0 {
		klineService = klineService.StartTime(req.StartTime)
	}

	// 如果指定了结束时间，添加到请求中
	if req.EndTime > 0 {
		klineService = klineService.EndTime(req.EndTime)
	}

	// 调用币安API获取K线数据
	binanceKlines, err := klineService.Do(l.ctx)
	if err != nil {
		l.Errorf("获取币安K线数据失败: %v", err)
		return nil, fmt.Errorf("获取币安K线数据失败: %v", err)
	}

	// 转换币安K线数据为项目数据结构
	klines := make([]types.KlineData, len(binanceKlines))
	for i, k := range binanceKlines {
		open, _ := strconv.ParseFloat(k.Open, 64)
		high, _ := strconv.ParseFloat(k.High, 64)
		low, _ := strconv.ParseFloat(k.Low, 64)
		close, _ := strconv.ParseFloat(k.Close, 64)
		volume, _ := strconv.ParseFloat(k.Volume, 64)

		klines[i] = types.KlineData{
			OpenTime:  k.OpenTime,
			Open:      open,
			High:      high,
			Low:       low,
			Close:     close,
			Volume:    volume,
			CloseTime: k.CloseTime,
		}
	}

	return &types.KlineResponse{
		Symbol: req.Symbol,
		Data:   klines,
	}, nil
}