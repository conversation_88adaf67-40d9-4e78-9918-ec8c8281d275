package logic

import (
	"context"
	"time"

	"zblockchain/app/bmgsdk/internal/svc"
	"zblockchain/app/bmgsdk/internal/types"
	"zblockchain/app/bmgsdk/model"
	"zblockchain/common/tool"

	"github.com/zeromicro/go-zero/core/logx"
)

type PayCallbackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPayCallbackLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PayCallbackLogic {
	return &PayCallbackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PayCallbackLogic) PayCallback(req *types.PayCallbackReq) (*types.PayCallbackRsp, error) {
	logx.Infof("PayCallback :%v", req)
	resp := &types.PayCallbackRsp{
		Ret: 0,
	}

	params := []struct {
		Key   string
		Value interface{}
	}{
		{"payTime", req.PayTime},
		{"orderId", req.OrderId},
		{"productId", req.ProductId},
		{"openId", req.OpenId},
		{"price", req.Price},
		{"extrasParams", req.ExtrasParams},
	}

	sign := tool.ApiSign(params, l.svcCtx.Config.ApiConfig.PaySecret, "", false, false)

	if sign != req.Sign {
		resp.Ret = 1
		resp.Msg = "验签失败"
		logx.Errorf("验签失败:%v,%v", sign, req.Sign)
	}else{
		pay := &model.Pay{
			OpenId: req.OpenId,
			OrderId: req.OrderId,
			ProductId: req.ProductId,
			PayTime: req.PayTime,
			Price: req.Price,
			Status: 0,
			CreateTime: time.Now(),
		}
		if _, err := l.svcCtx.PayModel.Insert(l.ctx, pay); err != nil {
			logx.Errorf("insert pay faild! value:%v, err:%v", pay, err)
			resp.Ret = 0
		}
	}

	return resp,nil
}
