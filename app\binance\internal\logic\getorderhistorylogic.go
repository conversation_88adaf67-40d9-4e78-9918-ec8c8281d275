package logic

import (
	"context"
	"fmt"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrderHistoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrderHistoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrderHistoryLogic {
	return &GetOrderHistoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrderHistoryLogic) GetOrderHistory(req *types.OrderHistoryRequest) (resp *types.OrderListResponse, err error) {
	// 创建币安期货客户端
	client := futures.NewClient(l.svcCtx.Config.Api<PERSON>ey, l.svcCtx.Config.ApiSecret)
	client.BaseURL = l.svcCtx.Config.BaseUrl.Http

	// 构建历史订单查询服务
	service := client.NewListOrdersService().Symbol(req.Symbol)

	// 设置可选参数
	if req.OrderId > 0 {
		service = service.OrderID(req.OrderId)
	}
	if req.StartTime > 0 {
		service = service.StartTime(req.StartTime)
	}
	if req.EndTime > 0 {
		service = service.EndTime(req.EndTime)
	}
	if req.Limit > 0 {
		service = service.Limit(req.Limit)
	} else {
		service = service.Limit(100) // 默认限制100条
	}

	// 获取历史订单
	orders, err := service.Do(l.ctx)
	if err != nil {
		l.Errorf("获取历史订单失败: %v", err)
		return nil, fmt.Errorf("获取历史订单失败: %v", err)
	}

	// 转换订单信息
	orderResponses := make([]types.OrderResponse, len(orders))
	for i, order := range orders {
		orderResponses[i] = types.OrderResponse{
			OrderId: order.OrderID,
			Symbol:  order.Symbol,
			Status:  string(order.Status),
		}
	}

	return &types.OrderListResponse{
		Orders: orderResponses,
	}, nil
}
