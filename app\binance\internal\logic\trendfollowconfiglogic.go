package logic

import (
	"context"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TrendFollowConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTrendFollowConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TrendFollowConfigLogic {
	return &TrendFollowConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TrendFollowConfigLogic) TrendFollowConfig(req *types.TrendFollowConfigRequest) (resp *types.TrendFollowConfigResponse, err error) {
	// 验证趋势跟随策略参数
	if req.EMAFast <= 0 || req.EMASlow <= 0 || req.EMAFast >= req.EMASlow {
		return &types.TrendFollowConfigResponse{
			Success: false,
			Message: "EMA参数错误：快线周期必须小于慢线周期且都大于0",
		}, nil
	}

	if req.InitialPositionRatio <= 0 || req.InitialPositionRatio > 1 {
		return &types.TrendFollowConfigResponse{
			Success: false,
			Message: "初始仓位比例必须在0-100%之间",
		}, nil
	}

	if req.MaxPositionRatio <= req.InitialPositionRatio || req.MaxPositionRatio > 1 {
		return &types.TrendFollowConfigResponse{
			Success: false,
			Message: "最大仓位比例必须大于初始仓位比例且不超过100%",
		}, nil
	}

	if req.CallbackPercent <= 0 || req.CallbackPercent > 0.1 {
		return &types.TrendFollowConfigResponse{
			Success: false,
			Message: "回调百分比必须在0-10%之间",
		}, nil
	}

	if req.MaxAddPositions < 0 || req.MaxAddPositions > 5 {
		return &types.TrendFollowConfigResponse{
			Success: false,
			Message: "最大加仓次数必须在0-5次之间",
		}, nil
	}

	if req.TakeProfitPercent <= 0 || req.TakeProfitPercent > 0.2 {
		return &types.TrendFollowConfigResponse{
			Success: false,
			Message: "止盈百分比必须在0-20%之间",
		}, nil
	}

	if req.StopLossPercent <= 0 || req.StopLossPercent > 0.1 {
		return &types.TrendFollowConfigResponse{
			Success: false,
			Message: "止损百分比必须在0-10%之间",
		}, nil
	}

	if req.RSILowerBound < 0 || req.RSIUpperBound > 100 || req.RSILowerBound >= req.RSIUpperBound {
		return &types.TrendFollowConfigResponse{
			Success: false,
			Message: "RSI参数错误：下界必须小于上界，且在0-100范围内",
		}, nil
	}

	// TODO: 这里可以将配置保存到数据库或Redis
	// 目前只是简单返回成功

	return &types.TrendFollowConfigResponse{
		Success: true,
		Message: "趋势跟随策略配置保存成功",
	}, nil
}
