package logic

import (
	"context"
	"fmt"
	"strconv"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetPositionsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPositionsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPositionsLogic {
	return &GetPositionsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPositionsLogic) GetPositions() (resp []types.PositionInfo, err error) {
	// 创建币安期货客户端
	client := futures.NewClient(l.svcCtx.Config.ApiKey, l.svcCtx.Config.ApiSecret)
	client.BaseURL = l.svcCtx.Config.BaseUrl.Http

	// 获取持仓信息
	positions, err := client.NewGetPositionRiskService().Do(l.ctx)
	if err != nil {
		l.Errorf("获取持仓信息失败: %v", err)
		return nil, fmt.Errorf("获取持仓信息失败: %v", err)
	}

	// 转换持仓信息
	result := make([]types.PositionInfo, 0)
	for _, pos := range positions {
		positionAmt, _ := strconv.ParseFloat(pos.PositionAmt, 64)

		// 只返回有持仓的交易对
		if positionAmt == 0 {
			continue
		}

		entryPrice, _ := strconv.ParseFloat(pos.EntryPrice, 64)
		markPrice, _ := strconv.ParseFloat(pos.MarkPrice, 64)
		unRealizedProfit, _ := strconv.ParseFloat(pos.UnRealizedProfit, 64)
		liquidationPrice, _ := strconv.ParseFloat(pos.LiquidationPrice, 64)
		leverage, _ := strconv.ParseFloat(pos.Leverage, 64)
		maxNotionalValue, _ := strconv.ParseFloat(pos.MaxNotionalValue, 64)
		isolatedMargin, _ := strconv.ParseFloat(pos.IsolatedMargin, 64)
		notional, _ := strconv.ParseFloat(pos.Notional, 64)
		isolatedWallet, _ := strconv.ParseFloat(pos.IsolatedWallet, 64)

		result = append(result, types.PositionInfo{
			Symbol:           pos.Symbol,
			PositionAmt:      positionAmt,
			EntryPrice:       entryPrice,
			MarkPrice:        markPrice,
			UnRealizedProfit: unRealizedProfit,
			LiquidationPrice: liquidationPrice,
			Leverage:         leverage,
			MaxNotionalValue: maxNotionalValue,
			MarginType:       pos.MarginType,
			IsolatedMargin:   isolatedMargin,
			IsAutoAddMargin:  pos.IsAutoAddMargin == "true",
			PositionSide:     string(pos.PositionSide),
			Notional:         notional,
			IsolatedWallet:   isolatedWallet,
			UpdateTime:       0, // PositionRisk doesn't have UpdateTime
		})
	}

	return result, nil
}
