// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.3

package handler

import (
	"net/http"

	"zblockchain/app/bmgsdk/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// login
				Method:  http.MethodPost,
				Path:    "/bmgsdk/loginVerify",
				Handler: loginVerifyHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/bmgsdk/payCallback",
				Handler: payCallbackHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/bmgsdk/payResult",
				Handler: payResultHandler(serverCtx),
			},
		},
	)
}
