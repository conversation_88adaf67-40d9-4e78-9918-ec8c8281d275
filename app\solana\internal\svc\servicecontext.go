package svc

import (
	"context"
	"zblockchain/app/solana/internal/config"

	"github.com/gagliardetto/solana-go/rpc/ws"
)

type ServiceContext struct {
	Config config.Config
	QN_ws_clinet *ws.Client
}

func NewServiceContext(c config.Config) *ServiceContext {
	client, err := ws.Connect(context.Background(), c.QuickNode.WssURL)
	if err != nil {
		panic(err)
	}
	return &ServiceContext{
		Config: c,
		QN_ws_clinet: client,
	}
}
