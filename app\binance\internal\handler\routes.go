// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.3

package handler

import (
	"net/http"

	"zblockchain/app/binance/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/account",
				Handler: GetAccountHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/breakthrough/backtest",
				Handler: BreakthroughBacktestHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/breakthrough/config",
				Handler: BreakthroughConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/breakthrough/status",
				Handler: BreakthroughStatusHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/health",
				Handler: HealthCheckHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/klines",
				Handler: GetKlinesHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/martingale/backtest",
				Handler: MartingaleBacktestHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/martingale/config",
				Handler: MartingaleConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/martingale/status",
				Handler: MartingaleStatusHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/order",
				Handler: CreateOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/orders/history",
				Handler: GetOrderHistoryHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/orders/open",
				Handler: GetOpenOrdersHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/positions",
				Handler: GetPositionsHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/price/:symbol",
				Handler: GetPriceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/trendfollow/backtest",
				Handler: TrendFollowBacktestHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/trendfollow/config",
				Handler: TrendFollowConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/trendfollow/status",
				Handler: TrendFollowStatusHandler(serverCtx),
			},
		},
	)
}
