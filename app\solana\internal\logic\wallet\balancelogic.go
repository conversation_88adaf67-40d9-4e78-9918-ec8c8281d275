package wallet

import (
	"context"
	"math/big"

	"zblockchain/app/solana/internal/logic"
	"zblockchain/app/solana/internal/svc"
	"zblockchain/app/solana/internal/types"

	"github.com/zeromicro/go-zero/core/logx"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
)

type BalanceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBalanceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BalanceLogic {
	return &BalanceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BalanceLogic) Balance() (resp *types.WalletInfo, err error) {
	logic.WalletKey()
	pubKey := solana.MustPublicKeyFromBase58("5oFAmsniqwH81sj5dvxnsrUuazZVo31eGJL1AKHs4rSm")
	balance := l.quickNodeBalance(pubKey)
	return &types.WalletInfo{
		Balance: balance,
	},nil
}

func (l *BalanceLogic) solanaSdkBalance(pubKey solana.PublicKey) float64 {
	endpoint := rpc.DevNet_RPC
	client := rpc.New(endpoint)

	out, err := client.GetBalance(
		context.TODO(),
		pubKey,
		rpc.CommitmentFinalized,
	)
	if err != nil {
		panic(err)
	}
	//   spew.Dump(out)
	//   spew.Dump(out.Value) // total lamports on the account; 1 sol = ********** lamports

	var lamportsOnAccount = new(big.Float).SetUint64(uint64(out.Value))
	// Convert lamports to sol:
	var solBalance = new(big.Float).Quo(lamportsOnAccount, new(big.Float).SetUint64(solana.LAMPORTS_PER_SOL))

	// WARNING: this is not a precise conversion.
	logx.Debugf("solBalance Text:%v", solBalance.Text('f', 10))
	balance,_ := solBalance.Float64()
	return balance
}

func (l *BalanceLogic) quickNodeBalance(pubKey solana.PublicKey) float64 {
	quickNodeRPC := l.svcCtx.Config.QuickNode.RPCURL

	// 配置 RPC 客户端
	client := rpc.New(quickNodeRPC)

	// 查询余额
	out, err := client.GetBalance(
		context.TODO(),
		pubKey,
		rpc.CommitmentFinalized,
	)
	if err != nil {
		logx.Errorf("查询余额失败: %v", err)
	}

	var lamportsOnAccount = new(big.Float).SetUint64(uint64(out.Value))
	// Convert lamports to sol:
	var solBalance = new(big.Float).Quo(lamportsOnAccount, new(big.Float).SetUint64(solana.LAMPORTS_PER_SOL))

	// WARNING: this is not a precise conversion.
	logx.Debugf("solBalance Text:%v", solBalance.Text('f', 10))
	balance,_ := solBalance.Float64()
	return balance
}