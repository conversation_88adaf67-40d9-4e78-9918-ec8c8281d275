package tcpgnet

import (
	"net"
	"sync"
	"zblockchain/common/network/codec"
	"zblockchain/common/network/core"

	"github.com/panjf2000/gnet/v2"
)

type Conn struct {
	sync.Mutex
	codec.ConnHelper
	conn      gnet.Conn
	closeFlag bool
	agent     core.Agent
}

// Init 初始化
func (gnetConn *Conn) Init(c gnet.Conn) {
	gnetConn.conn = c
	gnetConn.closeFlag = false
}

// Run 主逻辑
func (gnetConn *Conn) Run() {
}

// Close 关闭
func (gnetConn *Conn) Close() {
	if gnetConn.closeFlag {
		return
	}
	gnetConn.Lock()
	defer gnetConn.Unlock()
	if gnetConn.closeFlag {
		return
	}
	gnetConn.closeFlag = true
	if gnetConn.agent != nil {
		gnetConn.agent.OnClose()
	}
	if gnetConn.conn != nil {
		err := gnetConn.conn.Close()
		if err != nil {
			panic(err)
		}
	}

	gnetConn.agent = nil
	gnetConn.conn = nil
}

// Write 发送数据
func (gnetConn *Conn) Write(b []byte) (n int, err error) {
	if gnetConn.closeFlag {
		return
	}
	gnetConn.Lock()
	defer gnetConn.Unlock()
	if gnetConn.closeFlag {
		return
	}
	n = len(b)
	err = gnetConn.conn.AsyncWrite(b, nil)
	return
}

// LocalAddr 本地地址
func (gnetConn *Conn) LocalAddr() net.Addr {
	return gnetConn.conn.LocalAddr()
}

// RemoteAddr 远程地址
func (gnetConn *Conn) RemoteAddr() net.Addr {
	return gnetConn.conn.RemoteAddr()
}

func (gnetConn *Conn) setAgent(agent core.Agent) {
	gnetConn.agent = agent
	gnetConn.agent.OnConnect(gnetConn)
}

func (gnetConn *Conn) onMessage(b []byte) {
	if gnetConn.agent != nil && !gnetConn.closeFlag {

		gnetConn.agent.OnMessage(b)
	}
}
