package logic

import (
	"context"
	"fmt"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetOpenOrdersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOpenOrdersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOpenOrdersLogic {
	return &GetOpenOrdersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOpenOrdersLogic) GetOpenOrders(req *types.OpenOrdersRequest) (resp *types.OrderListResponse, err error) {
	// 创建币安期货客户端
	client := futures.NewClient(l.svcCtx.Config.<PERSON><PERSON><PERSON><PERSON>, l.svcCtx.Config.ApiSecret)
	client.BaseURL = l.svcCtx.Config.BaseUrl.Http

	// 构建当前挂单查询服务
	service := client.NewListOpenOrdersService()

	// 如果指定了交易对，添加到查询中
	if req.Symbol != "" {
		service = service.Symbol(req.Symbol)
	}

	// 获取当前挂单
	orders, err := service.Do(l.ctx)
	if err != nil {
		l.Errorf("获取当前挂单失败: %v", err)
		return nil, fmt.Errorf("获取当前挂单失败: %v", err)
	}

	// 转换订单信息
	orderResponses := make([]types.OrderResponse, len(orders))
	for i, order := range orders {
		orderResponses[i] = types.OrderResponse{
			OrderId: order.OrderID,
			Symbol:  order.Symbol,
			Status:  string(order.Status),
		}
	}

	return &types.OrderListResponse{
		Orders: orderResponses,
	}, nil
}
