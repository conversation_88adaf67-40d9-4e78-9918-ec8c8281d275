server{
    listen 3000;

    access_log /var/log/nginx/zgame.com_access.log;
    error_log /var/log/nginx/zgame.com_error.log;

    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header REMOTE-HOST $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    location /bmgsdk/ {
         proxy_pass http://127.0.0.1:3000;
    }

}

server{
    listen 8088;
    access_log /var/log/nginx/web.com_access.log;
    error_log /var/log/nginx/web.com_error.log;

    location / {
        root   /www/web;     #网站根目录
        index  index.html index.php;    #默认首页，改为index.php以便支持php网页
    }

}

server{
    listen 8099;
    access_log /var/log/nginx/dist.com_access.log;
    error_log /var/log/nginx/dist.com_error.log;

    location / {
        root   /www/dist;     #网站根目录
        index  index.html index.php;    #默认首页，改为index.php以便支持php网页
    }

}

