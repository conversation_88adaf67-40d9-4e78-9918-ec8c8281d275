// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	payFieldNames          = builder.RawFieldNames(&Pay{})
	payRows                = strings.Join(payFieldNames, ",")
	payRowsExpectAutoSet   = strings.Join(stringx.Remove(payFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	payRowsWithPlaceHolder = strings.Join(stringx.Remove(payFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"
)

type (
	payModel interface {
		Insert(ctx context.Context, data *Pay) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Pay, error)
		Update(ctx context.Context, data *Pay) error
		Delete(ctx context.Context, id int64) error
	}

	defaultPayModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Pay struct {
		Id         int64     `db:"id"`
		OpenId     string    `db:"openId"`
		OrderId    string    `db:"orderId"`
		ProductId  string    `db:"productId"`
		PayTime    int64     `db:"payTime"`
		Price      int64     `db:"price"`
		Status     int64     `db:"status"`
		CreateTime time.Time `db:"createTime"`
	}
)

func newPayModel(conn sqlx.SqlConn) *defaultPayModel {
	return &defaultPayModel{
		conn:  conn,
		table: "`pay`",
	}
}

func (m *defaultPayModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultPayModel) FindOne(ctx context.Context, id int64) (*Pay, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", payRows, m.table)
	var resp Pay
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPayModel) Insert(ctx context.Context, data *Pay) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, payRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OpenId, data.OrderId, data.ProductId, data.PayTime, data.Price, data.Status, data.CreateTime)
	return ret, err
}

func (m *defaultPayModel) Update(ctx context.Context, data *Pay) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, payRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.OpenId, data.OrderId, data.ProductId, data.PayTime, data.Price, data.Status, data.CreateTime, data.Id)
	return err
}

func (m *defaultPayModel) tableName() string {
	return m.table
}
