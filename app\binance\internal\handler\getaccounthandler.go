package handler

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"zblockchain/app/binance/internal/logic"
	"zblockchain/app/binance/internal/svc"
)

func GetAccountHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := logic.NewGetAccountLogic(r.Context(), svcCtx)
		resp, err := l.GetAccount()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
