package main

import (
	"context"
	"flag"
	"fmt"

	"zblockchain/app/solana/internal/config"
	"zblockchain/app/solana/internal/handler"
	"zblockchain/app/solana/internal/logic/subscribe"
	"zblockchain/app/solana/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/solana.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	ctx := svc.NewServiceContext(c)
	handler.RegisterHandlers(server, ctx)

	accountSub := subscribe.NewAccountSubscribe(context.Background(),ctx)
	accountSub.AddAccountSubscribe("8obbFr1dKgCHRBupgrPekVqqq7hrpEN6NphExCCRy7b1")

	// rootSub := subscribe.NewRootSubscribe(context.Background(),ctx)
	// rootSub.AddRootSubscribe()

	logsSub := subscribe.NewLogsSubscribe(context.Background(),ctx)
	logsSub.AddLogsSubscribe("8obbFr1dKgCHRBupgrPekVqqq7hrpEN6NphExCCRy7b1")

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
