package handler

import (
	"net/http"

	"zblockchain/app/bmgsdk/internal/logic"
	"zblockchain/app/bmgsdk/internal/svc"
	"zblockchain/app/bmgsdk/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func payCallbackHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.PayCallbackReq
		if err := httpx.Parse(r, &req); err != nil {
			logx.Errorf("payCallbackHandler err:%v", err.Error())
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}
		l := logic.NewPayCallbackLogic(r.Context(), svcCtx)
		resp, _ := l.PayCallback(&req)
		httpx.OkJsonCtx(r.Context(), w, resp)
		// if err != nil {
		// 	httpx.ErrorCtx(r.Context(), w, err)
		// } else {
		// 	httpx.OkJsonCtx(r.Context(), w, resp)
		// }
	}
}
