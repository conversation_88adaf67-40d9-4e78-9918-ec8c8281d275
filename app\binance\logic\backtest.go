package logic

import (
	"math"

	"github.com/markcheno/go-talib"
)

type Kline struct {
	Open      float64
	High      float64
	Low       float64
	Close     float64
	Volume    float64
	Timestamp int64
}

type FundingRate struct {
	Symbol    string
	Rate      float64
	Timestamp int64
}

// MartingaleLevel 马丁策略层级信息
type MartingaleLevel struct {
	Level      int     // 层级（1, 2, 3...）
	EntryPrice float64 // 入场价格
	Size       float64 // 仓位大小（USDT）
	Timestamp  int64   // 入场时间
}

type Backtest struct {
	Balance                                                    float64
	Position                                                   float64 // 正：多头；负：空头
	EntryPrice                                                 float64
	StopLossPrice                                              float64
	TakeProfitPrice                                            float64
	FeeRate                                                    float64
	Leverage                                                   float64
	StopLossMultiplier                                         float64
	TakeProfitMultiplier                                       float64
	MaintenanceMarginRate                                      float64
	TradeCount                                                 int
	WinCount, StopLossCount, TakeProfitCount, LiquidationCount int
	MaxDrawdown                                                float64
	PeakBalance                                                float64
	InitialBalance                                             float64
	LastFundingTime                                            int64
	NextStopLossPrice                                          float64 // 临时存储下次开仓的止损价格
	NextTakeProfitPrice                                        float64 // 临时存储下次开ware的止盈价格

	// 马丁策略相关字段
	MartingaleLevels                                           []MartingaleLevel // 马丁策略层级
	IsMartingaleStrategy                                       bool              // 是否使用马丁策略

	// 趋势跟随策略相关字段
	TrendFollowPositions                                       []TrendFollowPosition // 趋势跟随持仓层级
	IsTrendFollowStrategy                                      bool                  // 是否使用趋势跟随策略
	TrendFollowTotalInvestment                                 float64               // 趋势跟随总投资
	TrendFollowAvgEntryPrice                                   float64               // 趋势跟随平均入场价

	// 双向突破策略相关字段
	BreakthroughPositions                                      []BreakthroughPosition // 双向突破持仓
	IsBreakthroughStrategy                                     bool                   // 是否使用双向突破策略
	BreakthroughDailyPnL                                       float64                // 当日盈亏
	BreakthroughTotalPnL                                       float64                // 总盈亏
	BreakthroughPositionCounter                                int                    // 持仓计数器
	BreakthroughDailyStartBalance                              float64                // 当日起始余额
	BreakthroughLastResetTime                                  int64                  // 上次重置时间
}

func NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate float64) *Backtest {
	return &Backtest{
		Balance:               initialBalance,
		FeeRate:               feeRate,
		Leverage:              leverage,
		StopLossMultiplier:    stopLossMultiplier,
		TakeProfitMultiplier:  takeProfitMultiplier,
		MaintenanceMarginRate: maintenanceMarginRate,
		PeakBalance:           initialBalance,
		InitialBalance:        initialBalance,
	}
}

func (bt *Backtest) Run(klines []*Kline, fundingRates []*FundingRate, strategy StrategyFunc) {
	for i := 0; i < len(klines); i++ {
		// 更新追踪止盈
		if bt.Position != 0 {
			bt.updateTrailingStop(klines, i, klines[i].Close)
		}

		// 检查清算
		if bt.Position != 0 && bt.checkLiquidation(klines[i].Low) {
			bt.executeLiquidation(klines[i].Low, i)
			continue
		}

		// 检查止损/止盈
		if bt.Position > 0 { // 多头
			if klines[i].Low <= bt.StopLossPrice {
				bt.executeStopLoss(klines[i].Low, i)
				continue
			}
			if klines[i].High >= bt.TakeProfitPrice {
				bt.executeTakeProfit(klines[i].High, i)
				continue
			}
		} else if bt.Position < 0 { // 空头
			if klines[i].High >= bt.StopLossPrice {
				bt.executeStopLoss(klines[i].High, i)
				continue
			}
			if klines[i].Low <= bt.TakeProfitPrice {
				bt.executeTakeProfit(klines[i].Low, i)
				continue
			}
		}

		// 收取动态资金费率
		bt.applyFundingFee(klines[i].Timestamp, fundingRates)

		// 执行策略
		signal, price := strategy(klines, bt, i)

		// 更新最大回撤
		bt.updateMaxDrawdown(klines[i].Close)

		switch signal {
		case BuyLong:
			if bt.IsMartingaleStrategy {
				// 马丁策略做多
				bt.executeMartingaleBuy(price, klines[i].Timestamp)
			} else if bt.IsTrendFollowStrategy {
				// 趋势跟随策略做多 - 需要传递参数，这里使用默认参数
				defaultParams := GetDefaultTrendFollowParams(bt.InitialBalance)
				bt.executeTrendFollowBuy(price, klines[i].Timestamp, defaultParams)
			} else {
				// 传统策略做多
				bt.Position = (bt.Balance * bt.Leverage) / price
				bt.EntryPrice = price
				bt.StopLossPrice = bt.NextStopLossPrice
				bt.TakeProfitPrice = bt.NextTakeProfitPrice
				bt.NextStopLossPrice = 0
				bt.NextTakeProfitPrice = 0
				bt.Balance = bt.Balance * (1 - 1/bt.Leverage)
				bt.TradeCount++
				bt.LastFundingTime = klines[i].Timestamp
			}
		case BuyShort:
			// 做空（马丁策略暂不支持做空）
			bt.Position = -(bt.Balance * bt.Leverage) / price
			bt.EntryPrice = price
			bt.StopLossPrice = bt.NextStopLossPrice
			bt.TakeProfitPrice = bt.NextTakeProfitPrice
			bt.NextStopLossPrice = 0
			bt.NextTakeProfitPrice = 0
			bt.Balance = bt.Balance * (1 - 1/bt.Leverage)
			bt.TradeCount++
			bt.LastFundingTime = klines[i].Timestamp
		case SellLong, SellShort:
			if bt.IsMartingaleStrategy {
				// 马丁策略平仓
				bt.executeMartingaleSell(price)
			} else if bt.IsTrendFollowStrategy {
				// 趋势跟随策略平仓
				bt.executeTrendFollowSell(price)
			} else {
				// 传统策略平仓
				profit := (price - bt.EntryPrice) * bt.Position
				if bt.Position < 0 {
					profit = (bt.EntryPrice - price) * math.Abs(bt.Position)
				}
				fee := math.Abs(bt.Position) * price * bt.FeeRate
				bt.Balance += profit - fee + (math.Abs(bt.Position) * price / bt.Leverage)
				if profit > 0 {
					bt.WinCount++
				}
				bt.Position = 0
				bt.EntryPrice = 0
				bt.StopLossPrice = 0
				bt.TakeProfitPrice = 0
				bt.TradeCount++
			}
		}
	}

	// 最后更新回撤
	bt.updateMaxDrawdown(klines[len(klines)-1].Close)
}

func (bt *Backtest) updateTrailingStop(klines []*Kline, index int, currentPrice float64) {
	const atrPeriod = 14 // ATR 周期

	// 确保有足够数据计算 ATR
	if index < atrPeriod || len(klines) <= index {
		return
	}

	// 提取最近 atrPeriod+1 根 K 线数据（为 PrevClose 留余量）
	start := index - atrPeriod
	if start < 0 {
		start = 0
	}
	if index-start+1 < atrPeriod+1 {
		return // 数据不足
	}

	// 构造 highs, lows, closes 动态数组
	highs := make([]float64, 0, atrPeriod+1)
	lows := make([]float64, 0, atrPeriod+1)
	closes := make([]float64, 0, atrPeriod+1)
	for i := start; i <= index; i++ {
		highs = append(highs, klines[i].High)
		lows = append(lows, klines[i].Low)
		closes = append(closes, klines[i].Close)
	}

	// 验证数组长度
	if len(highs) < atrPeriod+1 {
		return // 数据不足
	}

	// 计算 ATR
	atr := talib.Atr(highs, lows, closes, atrPeriod)
	if len(atr) == 0 {
		return // ATR 计算失败
	}
	currentAtr := atr[len(atr)-1]

	// 更新追踪止盈
	if bt.Position > 0 { // 多头
		newTakeProfit := math.Max(bt.TakeProfitPrice, currentPrice-currentAtr*bt.TakeProfitMultiplier)
		bt.TakeProfitPrice = newTakeProfit
	} else if bt.Position < 0 { // 空头
		newTakeProfit := math.Min(bt.TakeProfitPrice, currentPrice+currentAtr*bt.TakeProfitMultiplier)
		bt.TakeProfitPrice = newTakeProfit
	}
}

func (bt *Backtest) executeStopLoss(price float64, _ int) {
	profit := (price - bt.EntryPrice) * bt.Position
	if bt.Position < 0 {
		profit = (bt.EntryPrice - price) * math.Abs(bt.Position)
	}
	fee := math.Abs(bt.Position) * price * bt.FeeRate
	bt.Balance += profit - fee + (math.Abs(bt.Position) * price / bt.Leverage)
	bt.StopLossCount++
	bt.TradeCount++
	if profit > 0 {
		bt.WinCount++
	}
	bt.Position = 0
	bt.EntryPrice = 0
	bt.StopLossPrice = 0
	bt.TakeProfitPrice = 0
}

func (bt *Backtest) executeTakeProfit(price float64, _ int) {
	profit := (price - bt.EntryPrice) * bt.Position
	if bt.Position < 0 {
		profit = (bt.EntryPrice - price) * math.Abs(bt.Position)
	}
	fee := math.Abs(bt.Position) * price * bt.FeeRate
	bt.Balance += profit - fee + (math.Abs(bt.Position) * price / bt.Leverage)
	bt.TakeProfitCount++
	bt.TradeCount++
	if profit > 0 {
		bt.WinCount++
	}
	bt.Position = 0
	bt.EntryPrice = 0
	bt.StopLossPrice = 0
	bt.TakeProfitPrice = 0
}

func (bt *Backtest) executeLiquidation(price float64, _ int) {
	profit := (price - bt.EntryPrice) * bt.Position
	if bt.Position < 0 {
		profit = (bt.EntryPrice - price) * math.Abs(bt.Position)
	}
	fee := math.Abs(bt.Position) * price * bt.FeeRate
	bt.Balance += profit - fee + (math.Abs(bt.Position) * price / bt.Leverage)
	if bt.Balance < 0 {
		bt.Balance = 0
	}
	bt.LiquidationCount++
	bt.TradeCount++
	bt.Position = 0
	bt.EntryPrice = 0
	bt.StopLossPrice = 0
	bt.TakeProfitPrice = 0
}

func (bt *Backtest) checkLiquidation(currentPrice float64) bool {
	if bt.Position == 0 {
		return false
	}

	equity := bt.Balance
	if bt.Position > 0 {
		equity += (currentPrice - bt.EntryPrice) * bt.Position
	} else {
		equity += (bt.EntryPrice - currentPrice) * math.Abs(bt.Position)
	}
	positionValue := math.Abs(bt.Position) * currentPrice
	maintenanceMargin := positionValue * bt.MaintenanceMarginRate

	return equity < maintenanceMargin
}

func (bt *Backtest) applyFundingFee(currentTimestamp int64, fundingRates []*FundingRate) {
	if bt.Position == 0 {
		return
	}

	fundingInterval := int64(8 * 60 * 60 * 1000)
	if currentTimestamp < bt.LastFundingTime+fundingInterval {
		return
	}

	var fundingRate float64
	for _, fr := range fundingRates {
		if fr.Timestamp >= bt.LastFundingTime && fr.Timestamp <= currentTimestamp {
			fundingRate = fr.Rate
			break
		}
	}

	fundingFee := math.Abs(bt.Position) * bt.EntryPrice * math.Abs(fundingRate)
	if bt.Position > 0 && fundingRate > 0 {
		bt.Balance -= fundingFee
	} else if bt.Position > 0 && fundingRate < 0 {
		bt.Balance += fundingFee
	} else if bt.Position < 0 && fundingRate > 0 {
		bt.Balance += fundingFee
	} else if bt.Position < 0 && fundingRate < 0 {
		bt.Balance -= fundingFee
	}

	bt.LastFundingTime = currentTimestamp
}

func (bt *Backtest) updateMaxDrawdown(currentPrice float64) {
	equity := bt.Balance
	if bt.Position > 0 {
		equity += bt.Position * (currentPrice - bt.EntryPrice + currentPrice/bt.Leverage)
	} else if bt.Position < 0 {
		equity += math.Abs(bt.Position) * (bt.EntryPrice - currentPrice + currentPrice/bt.Leverage)
	}
	if equity > bt.PeakBalance {
		bt.PeakBalance = equity
	}
	drawdown := (bt.PeakBalance - equity) / bt.PeakBalance
	if drawdown > bt.MaxDrawdown {
		bt.MaxDrawdown = drawdown
	}
}

// calculateMartingalePnL 计算马丁策略总盈亏
func (bt *Backtest) calculateMartingalePnL(currentPrice float64) float64 {
	totalPnL := 0.0
	for _, level := range bt.MartingaleLevels {
		// 计算每层的盈亏
		pnl := (currentPrice - level.EntryPrice) * (level.Size / level.EntryPrice)
		totalPnL += pnl
	}
	return totalPnL
}

// calculateTotalInvestment 计算马丁策略总投资
func (bt *Backtest) calculateTotalInvestment() float64 {
	totalInvestment := 0.0
	for _, level := range bt.MartingaleLevels {
		totalInvestment += level.Size
	}
	return totalInvestment
}

// addMartingaleLevel 添加马丁策略层级
func (bt *Backtest) addMartingaleLevel(price float64, size float64, timestamp int64) {
	level := MartingaleLevel{
		Level:      len(bt.MartingaleLevels) + 1,
		EntryPrice: price,
		Size:       size,
		Timestamp:  timestamp,
	}
	bt.MartingaleLevels = append(bt.MartingaleLevels, level)
}

// clearMartingaleLevels 清空马丁策略层级
func (bt *Backtest) clearMartingaleLevels() {
	bt.MartingaleLevels = []MartingaleLevel{}
}

// addTrendFollowPosition 添加趋势跟随持仓
func (bt *Backtest) addTrendFollowPosition(price float64, size float64, timestamp int64) {
	position := TrendFollowPosition{
		Level:      len(bt.TrendFollowPositions) + 1,
		EntryPrice: price,
		Size:       size,
		Timestamp:  timestamp,
		IsActive:   true,
	}
	bt.TrendFollowPositions = append(bt.TrendFollowPositions, position)

	// 更新总投资和平均入场价
	bt.TrendFollowTotalInvestment += size
	totalValue := 0.0
	totalSize := 0.0
	for _, pos := range bt.TrendFollowPositions {
		if pos.IsActive {
			totalValue += pos.Size
			totalSize += pos.Size / pos.EntryPrice
		}
	}
	if totalSize > 0 {
		bt.TrendFollowAvgEntryPrice = totalValue / totalSize
	}
}

// clearTrendFollowPositions 清空趋势跟随持仓
func (bt *Backtest) clearTrendFollowPositions() {
	bt.TrendFollowPositions = []TrendFollowPosition{}
	bt.TrendFollowTotalInvestment = 0
	bt.TrendFollowAvgEntryPrice = 0
}

// calculateTrendFollowPnL 计算趋势跟随策略总盈亏
func (bt *Backtest) calculateTrendFollowPnL(currentPrice float64) float64 {
	totalPnL := 0.0
	for _, pos := range bt.TrendFollowPositions {
		if pos.IsActive {
			// 计算每层的盈亏
			pnl := (currentPrice - pos.EntryPrice) * (pos.Size / pos.EntryPrice)
			totalPnL += pnl
		}
	}
	return totalPnL
}

// getTrendFollowTotalSize 获取趋势跟随策略总仓位大小
func (bt *Backtest) getTrendFollowTotalSize() float64 {
	totalSize := 0.0
	for _, pos := range bt.TrendFollowPositions {
		if pos.IsActive {
			totalSize += pos.Size / pos.EntryPrice
		}
	}
	return totalSize
}

// executeMartingaleBuy 执行马丁策略买入
func (bt *Backtest) executeMartingaleBuy(price float64, timestamp int64) {
	// 计算仓位大小
	var positionSize float64
	if len(bt.MartingaleLevels) == 0 {
		// 第一层，使用初始仓位大小
		positionSize = bt.InitialBalance * 0.1 // 使用10%的资金作为初始仓位
	} else {
		// 后续层级，使用倍数加仓
		lastLevel := bt.MartingaleLevels[len(bt.MartingaleLevels)-1]
		positionSize = lastLevel.Size * 2.0 // 默认2倍加仓
	}

	// 检查资金是否足够
	if positionSize > bt.Balance {
		positionSize = bt.Balance * 0.9 // 最多使用90%的剩余资金
	}

	// 添加马丁层级
	bt.addMartingaleLevel(price, positionSize, timestamp)

	// 更新总仓位
	totalPosition := 0.0
	for _, level := range bt.MartingaleLevels {
		totalPosition += level.Size / level.EntryPrice
	}
	bt.Position = totalPosition

	// 计算平均入场价
	totalValue := 0.0
	totalSize := 0.0
	for _, level := range bt.MartingaleLevels {
		totalValue += level.Size
		totalSize += level.Size / level.EntryPrice
	}
	bt.EntryPrice = totalValue / totalSize

	// 扣除资金
	bt.Balance -= positionSize
	bt.TradeCount++
}

// executeMartingaleSell 执行马丁策略卖出
func (bt *Backtest) executeMartingaleSell(price float64) {
	if len(bt.MartingaleLevels) == 0 {
		return
	}

	// 计算总盈亏
	totalPnL := bt.calculateMartingalePnL(price)
	totalInvestment := bt.calculateTotalInvestment()

	// 计算手续费
	fee := bt.Position * price * bt.FeeRate

	// 更新余额
	bt.Balance += totalInvestment + totalPnL - fee

	// 记录盈利情况
	if totalPnL > 0 {
		bt.WinCount++
	}

	// 清空马丁层级
	bt.clearMartingaleLevels()
	bt.Position = 0
	bt.EntryPrice = 0
	bt.TradeCount++
}

// executeTrendFollowBuy 执行趋势跟随策略买入
func (bt *Backtest) executeTrendFollowBuy(price float64, timestamp int64, params TrendFollowParams) {
	// 计算仓位大小
	var positionSize float64
	if len(bt.TrendFollowPositions) == 0 {
		// 第一层，使用初始仓位比例
		positionSize = bt.InitialBalance * params.InitialPositionRatio
	} else {
		// 后续层级，继续使用初始仓位比例
		positionSize = bt.InitialBalance * params.InitialPositionRatio
	}

	// 检查是否超过最大持仓比例
	currentInvestmentRatio := bt.TrendFollowTotalInvestment / bt.InitialBalance
	if currentInvestmentRatio + params.InitialPositionRatio > params.MaxPositionRatio {
		// 调整仓位大小，不超过最大持仓比例
		maxAdditionalInvestment := bt.InitialBalance * (params.MaxPositionRatio - currentInvestmentRatio)
		if maxAdditionalInvestment > 0 {
			positionSize = maxAdditionalInvestment
		} else {
			return // 已达到最大持仓，不再加仓
		}
	}

	// 检查资金是否足够
	if positionSize > bt.Balance {
		positionSize = bt.Balance * 0.9 // 最多使用90%的剩余资金
	}

	// 添加趋势跟随持仓
	bt.addTrendFollowPosition(price, positionSize, timestamp)

	// 更新总仓位
	bt.Position = bt.getTrendFollowTotalSize()
	bt.EntryPrice = bt.TrendFollowAvgEntryPrice

	// 扣除资金
	bt.Balance -= positionSize
	bt.TradeCount++
}

// executeTrendFollowSell 执行趋势跟随策略卖出
func (bt *Backtest) executeTrendFollowSell(price float64) {
	if len(bt.TrendFollowPositions) == 0 {
		return
	}

	// 计算总盈亏
	totalPnL := bt.calculateTrendFollowPnL(price)

	// 计算手续费
	fee := bt.Position * price * bt.FeeRate

	// 更新余额
	bt.Balance += bt.TrendFollowTotalInvestment + totalPnL - fee

	// 记录盈利情况
	if totalPnL > 0 {
		bt.WinCount++
	}

	// 清空趋势跟随持仓
	bt.clearTrendFollowPositions()
	bt.Position = 0
	bt.EntryPrice = 0
	bt.TradeCount++
}

// addBreakthroughPosition 添加双向突破持仓
func (bt *Backtest) addBreakthroughPosition(symbol, direction string, price, size, leverage, takeProfit, stopLoss float64, timestamp int64) {
	bt.BreakthroughPositionCounter++
	position := BreakthroughPosition{
		ID:         bt.BreakthroughPositionCounter,
		Symbol:     symbol,
		Direction:  direction,
		EntryPrice: price,
		Size:       size,
		Leverage:   leverage,
		TakeProfit: takeProfit,
		StopLoss:   stopLoss,
		Timestamp:  timestamp,
		IsActive:   true,
	}
	bt.BreakthroughPositions = append(bt.BreakthroughPositions, position)
}

// removeBreakthroughPosition 移除双向突破持仓
func (bt *Backtest) removeBreakthroughPosition(positionID int) {
	for i, pos := range bt.BreakthroughPositions {
		if pos.ID == positionID && pos.IsActive {
			bt.BreakthroughPositions[i].IsActive = false
			break
		}
	}
}

// GetActiveBreakthroughPositions 获取活跃的双向突破持仓
func (bt *Backtest) GetActiveBreakthroughPositions() []BreakthroughPosition {
	var activePositions []BreakthroughPosition
	for _, pos := range bt.BreakthroughPositions {
		if pos.IsActive {
			activePositions = append(activePositions, pos)
		}
	}
	return activePositions
}

// calculateBreakthroughPnL 计算双向突破策略总盈亏
func (bt *Backtest) calculateBreakthroughPnL(currentPrices map[string]float64) float64 {
	totalPnL := 0.0
	for _, pos := range bt.BreakthroughPositions {
		if pos.IsActive {
			currentPrice, exists := currentPrices[pos.Symbol]
			if !exists {
				continue
			}

			var pnl float64
			if pos.Direction == "LONG" {
				pnl = (currentPrice - pos.EntryPrice) / pos.EntryPrice * pos.Size * pos.Leverage
			} else { // SHORT
				pnl = (pos.EntryPrice - currentPrice) / pos.EntryPrice * pos.Size * pos.Leverage
			}
			totalPnL += pnl
		}
	}
	return totalPnL
}

// checkBreakthroughStopConditions 检查双向突破策略止盈止损条件
func (bt *Backtest) checkBreakthroughStopConditions(currentPrices map[string]float64) []int {
	var positionsToClose []int

	for _, pos := range bt.BreakthroughPositions {
		if !pos.IsActive {
			continue
		}

		currentPrice, exists := currentPrices[pos.Symbol]
		if !exists {
			continue
		}

		shouldClose := false

		if pos.Direction == "LONG" {
			// 做多：价格达到止盈或跌破止损
			if currentPrice >= pos.TakeProfit || currentPrice <= pos.StopLoss {
				shouldClose = true
			}
		} else { // SHORT
			// 做空：价格跌到止盈或涨破止损
			if currentPrice <= pos.TakeProfit || currentPrice >= pos.StopLoss {
				shouldClose = true
			}
		}

		if shouldClose {
			positionsToClose = append(positionsToClose, pos.ID)
		}
	}

	return positionsToClose
}

// executeBreakthroughClose 执行双向突破策略平仓
func (bt *Backtest) executeBreakthroughClose(positionID int, currentPrice float64) {
	for i, pos := range bt.BreakthroughPositions {
		if pos.ID == positionID && pos.IsActive {
			// 计算盈亏
			var pnl float64
			if pos.Direction == "LONG" {
				pnl = (currentPrice - pos.EntryPrice) / pos.EntryPrice * pos.Size * pos.Leverage
			} else { // SHORT
				pnl = (pos.EntryPrice - currentPrice) / pos.EntryPrice * pos.Size * pos.Leverage
			}

			// 计算手续费
			fee := pos.Size * pos.Leverage * currentPrice * bt.FeeRate
			netPnL := pnl - fee

			// 更新余额和统计
			bt.Balance += netPnL
			bt.BreakthroughDailyPnL += netPnL
			bt.BreakthroughTotalPnL += netPnL

			if netPnL > 0 {
				bt.WinCount++
			}

			// 标记持仓为非活跃
			bt.BreakthroughPositions[i].IsActive = false
			bt.TradeCount++
			break
		}
	}
}

// resetBreakthroughDaily 重置双向突破策略每日统计
func (bt *Backtest) resetBreakthroughDaily(timestamp int64) {
	bt.BreakthroughDailyPnL = 0
	bt.BreakthroughDailyStartBalance = bt.Balance
	bt.BreakthroughLastResetTime = timestamp
}

// checkBreakthroughDailyTarget 检查是否达到每日目标
func (bt *Backtest) checkBreakthroughDailyTarget(params BreakthroughParams) bool {
	return bt.BreakthroughDailyPnL >= params.DailyTarget
}

// checkBreakthroughMaxDrawdown 检查是否超过最大回撤
func (bt *Backtest) checkBreakthroughMaxDrawdown(params BreakthroughParams) bool {
	currentDrawdown := bt.BreakthroughDailyStartBalance - bt.Balance
	return currentDrawdown >= params.MaxDrawdown
}

// RunBreakthrough 运行双向突破策略回测
func (bt *Backtest) RunBreakthrough(klines []*Kline, fundingRates []*FundingRate, params BreakthroughParams, strategy func([]*Kline, *Backtest, int, BreakthroughParams, string, map[string]float64) (TradeSignal, float64, string)) {
	// 初始化双向突破策略
	bt.IsBreakthroughStrategy = true
	bt.BreakthroughDailyStartBalance = bt.Balance
	bt.BreakthroughLastResetTime = klines[0].Timestamp

	// 模拟多币种价格数据（实际应该从API获取）
	allSymbolPrices := map[string]float64{
		"BTCUSDT": klines[0].Close,
		"ETHUSDT": klines[0].Close * 0.06, // 模拟ETH价格
		"SOLUSDT": klines[0].Close * 0.002, // 模拟SOL价格
		"BNBUSDT": klines[0].Close * 0.01,  // 模拟BNB价格
	}

	for i := 1; i < len(klines); i++ {
		// 更新模拟价格
		priceChange := (klines[i].Close - klines[i-1].Close) / klines[i-1].Close
		for symbol := range allSymbolPrices {
			if symbol == "BTCUSDT" {
				allSymbolPrices[symbol] = klines[i].Close
			} else {
				// 其他币种跟随BTC变化，但有不同的波动率
				volatilityMultiplier := 1.0
				switch symbol {
				case "ETHUSDT":
					volatilityMultiplier = 1.2
				case "SOLUSDT":
					volatilityMultiplier = 1.5
				case "BNBUSDT":
					volatilityMultiplier = 0.8
				}
				allSymbolPrices[symbol] *= (1 + priceChange*volatilityMultiplier)
			}
		}

		// 检查现有持仓的止盈止损
		positionsToClose := bt.checkBreakthroughStopConditions(allSymbolPrices)
		for _, positionID := range positionsToClose {
			// 找到对应持仓并平仓
			for _, pos := range bt.BreakthroughPositions {
				if pos.ID == positionID && pos.IsActive {
					currentPrice := allSymbolPrices[pos.Symbol]
					bt.executeBreakthroughClose(positionID, currentPrice)
					break
				}
			}
		}

		// 应用资金费率
		bt.applyFundingFee(klines[i].Timestamp, fundingRates)

		// 执行策略
		signal, price, symbol := strategy(klines, bt, i, params, "BTCUSDT", allSymbolPrices)

		// 更新最大回撤
		bt.updateMaxDrawdown(klines[i].Close)

		// 处理交易信号
		switch signal {
		case BuyLong:
			if len(bt.GetActiveBreakthroughPositions()) < params.MaxPositions {
				// 计算动态参数
				leverage := calculateDynamicLeverage(params, 0, 0, 0, price) // 简化处理
				volatility := 0.02 // 简化处理
				takeProfitPercent := calculateDynamicTakeProfit(params, volatility)
				stopLossPercent := calculateDynamicStopLoss(params, volatility)

				// 计算止盈止损价格
				takeProfit := price * (1 + takeProfitPercent)
				stopLoss := price * (1 - stopLossPercent)

				// 添加多头持仓
				bt.addBreakthroughPosition(symbol, "LONG", price, params.PositionSize, leverage, takeProfit, stopLoss, klines[i].Timestamp)

				// 扣除保证金
				margin := params.PositionSize / leverage
				bt.Balance -= margin
			}
		case BuyShort:
			if len(bt.GetActiveBreakthroughPositions()) < params.MaxPositions {
				// 计算动态参数
				leverage := calculateDynamicLeverage(params, 0, 0, 0, price) // 简化处理
				volatility := 0.02 // 简化处理
				takeProfitPercent := calculateDynamicTakeProfit(params, volatility)
				stopLossPercent := calculateDynamicStopLoss(params, volatility)

				// 计算止盈止损价格（做空相反）
				takeProfit := price * (1 - takeProfitPercent)
				stopLoss := price * (1 + stopLossPercent)

				// 添加空头持仓
				bt.addBreakthroughPosition(symbol, "SHORT", price, params.PositionSize, leverage, takeProfit, stopLoss, klines[i].Timestamp)

				// 扣除保证金
				margin := params.PositionSize / leverage
				bt.Balance -= margin
			}
		}

		// 检查每日目标和最大回撤
		if bt.checkBreakthroughDailyTarget(params) {
			// 达到每日目标，平所有仓位并停止
			for _, pos := range bt.BreakthroughPositions {
				if pos.IsActive {
					currentPrice := allSymbolPrices[pos.Symbol]
					bt.executeBreakthroughClose(pos.ID, currentPrice)
				}
			}
			break
		}

		if bt.checkBreakthroughMaxDrawdown(params) {
			// 超过最大回撤，强制平所有仓位并停止
			for _, pos := range bt.BreakthroughPositions {
				if pos.IsActive {
					currentPrice := allSymbolPrices[pos.Symbol]
					bt.executeBreakthroughClose(pos.ID, currentPrice)
				}
			}
			break
		}
	}

	// 最后更新回撤
	bt.updateMaxDrawdown(klines[len(klines)-1].Close)
}
