package handler

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"zblockchain/app/binance/internal/logic"
	"zblockchain/app/binance/internal/svc"
)

func MartingaleStatusHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := logic.NewMartingaleStatusLogic(r.Context(), svcCtx)
		resp, err := l.MartingaleStatus()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
