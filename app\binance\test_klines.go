package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"zblockchain/app/binance/internal/config"
	"zblockchain/app/binance/internal/logic"
	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/zeromicro/go-zero/core/conf"
)

func main1() {
	// 加载配置
	var c config.Config
	conf.MustLoad("etc/binance.yaml", &c)

	// 创建服务上下文
	svcCtx := svc.NewServiceContext(c)

	// 创建GetKlines逻辑
	ctx := context.Background()
	l := logic.NewGetKlinesLogic(ctx, svcCtx)

	// 测试请求
	req := &types.KlineRequest{
		Symbol:   "BTCUSDT",
		Interval: "1h",
		Limit:    5, // 只获取5条数据用于测试
	}

	fmt.Printf("正在请求币安真实K线数据...\n")
	fmt.Printf("交易对: %s\n", req.Symbol)
	fmt.Printf("时间间隔: %s\n", req.Interval)
	fmt.Printf("数量限制: %d\n", req.Limit)
	fmt.Println("---")

	// 调用GetKlines方法
	resp, err := l.GetKlines(req)
	if err != nil {
		log.Fatalf("获取K线数据失败: %v", err)
	}

	// 打印结果
	fmt.Printf("成功获取 %s K线数据，共 %d 条记录:\n", resp.Symbol, len(resp.Data))
	fmt.Println("---")

	for i, kline := range resp.Data {
		fmt.Printf("第 %d 条K线:\n", i+1)
		fmt.Printf("  开盘时间: %d\n", kline.OpenTime)
		fmt.Printf("  开盘价: %.2f\n", kline.Open)
		fmt.Printf("  最高价: %.2f\n", kline.High)
		fmt.Printf("  最低价: %.2f\n", kline.Low)
		fmt.Printf("  收盘价: %.2f\n", kline.Close)
		fmt.Printf("  成交量: %.2f\n", kline.Volume)
		fmt.Printf("  收盘时间: %d\n", kline.CloseTime)
		fmt.Println("---")
	}

	// 输出JSON格式的响应（用于验证数据结构）
	jsonData, _ := json.MarshalIndent(resp, "", "  ")
	fmt.Println("JSON格式响应:")
	fmt.Println(string(jsonData))
}
