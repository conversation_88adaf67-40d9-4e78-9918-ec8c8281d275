{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "solana",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/app/solana/solana.go"
        },
        {
            "name": "base",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/app/base/base.go"
        },
        {
            "name": "algorithm",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/app/algorithm/algorithm.go"
        },
        {
            "name": "bmgsdk",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/app/bmgsdk/bmgsdk.go"
        },
        {
            "name": "binance",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/app/binance/binance.go"
        }
    ],
    "compounds": [
        {
            "name": "zblockchain",
            "configurations": [
                "solana",
                "base"
            ]
        }
    ]
    
}