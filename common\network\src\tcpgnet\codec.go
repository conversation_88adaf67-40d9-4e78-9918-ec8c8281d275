package tcpgnet

import (
	"encoding/binary"
	"encoding/json"
	"errors"
	"zblockchain/common/network/protocol"

	"github.com/panjf2000/gnet/v2"
)

const (
	magicNumber     = 1314
	magicNumberSize = 4
	bodySize        = 4
)

var errIncompletePacket = errors.New("incomplete packet")
var magicNumberBytes []byte

type GNetCodec struct {
}

func NewGNetCodec() *GNetCodec {
	return &GNetCodec{}
}

func (codec *GNetCodec) Encode(buf []byte) ([]byte, error) {
	bodyOffset := magicNumberSize + bodySize
	msgLen := bodyOffset + len(buf)

	data := make([]byte, msgLen)
	copy(data, magicNumberBytes)

	binary.BigEndian.PutUint32(data[magicNumberSize:bodyOffset], uint32(len(buf)))
	copy(data[bodyOffset:msgLen], buf)
	return data, nil
}

func (codec *GNetCodec) Decode(c gnet.Conn) ([]byte, error) {
	bodyOffset := magicNumberSize + bodySize
	buf, _ := c.Peek(bodyOffset)
	if len(buf) < bodyOffset {
		return nil, errIncompletePacket
	}
	bodyLen := binary.BigEndian.Uint32(buf[:magicNumberSize])
	requestId := binary.BigEndian.Uint32(buf[magicNumberSize:bodyOffset])
	msgLen := int(bodyLen) + magicNumberSize
	buf, _ = c.Peek(msgLen)
	_, _ = c.Discard(msgLen)

	b := buf[bodyOffset:msgLen]
	p := protocol.Protocol{
		RequestId:  requestId,
		ReceiveMsg: b,
	}
	data, _ := json.Marshal(p)
	return data, nil
}
