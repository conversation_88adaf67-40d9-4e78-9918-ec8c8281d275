# 币安环境配置示例

## 测试网配置 (推荐用于开发)

```yaml
Name: binance
Host: 0.0.0.0
Port: 8888

BaseUrl:
  Http: https://testnet.binance.vision
  Ws: wss://stream.testnet.binance.vision

# 测试网API密钥 (从 https://testnet.binance.vision/ 获取)
ApiKey: HA2iUKJY71FkQ8cqqihYvwE4O5C5ueCP0H63xOKHtrkJ6c0MBHhXNSB67UXxgPJL
ApiSecret: zxlDGlWBwkgxpV1asLQMY1fmswB91D5xQDtbFuThRAv37ysREvuPfG3XtvzTwow5

Redis:
  Host: ***************:6379
  Type: node
  Pass:
  Key:
DB:
  DataSource: dev:yrmQzgpNznKj22Hx@tcp(***********:3306)/app?charset=utf8mb4&parseTime=True&loc=Local
```

## 生产网配置 (用于实际交易)

```yaml
Name: binance
Host: 0.0.0.0
Port: 8888

BaseUrl:
  Http: https://fapi.binance.com
  Ws: wss://fstream.binance.com

# 生产网API密钥 (从 https://www.binance.com/ 获取)
ApiKey: [您的生产环境API Key]
ApiSecret: [您的生产环境API Secret]

Redis:
  Host: ***************:6379
  Type: node
  Pass:
  Key:
DB:
  DataSource: dev:yrmQzgpNznKj22Hx@tcp(***********:3306)/app?charset=utf8mb4&parseTime=True&loc=Local
```

## 环境对比

| 特性 | 测试网 | 生产网 |
|------|--------|--------|
| 资金 | 虚拟资金 | 真实资金 |
| K线数据 | 真实市场数据 | 真实市场数据 |
| 交易 | 模拟交易 | 真实交易 |
| 风险 | 无风险 | 有真实资金风险 |
| API限制 | 相对宽松 | 严格限制 |
| 适用场景 | 开发、测试 | 生产环境 |

## 如何切换环境

### 1. 切换到测试网
```bash
# 修改 app/binance/etc/binance.yaml
BaseUrl:
  Http: https://testnet.binance.vision
  Ws: wss://stream.testnet.binance.vision
```

### 2. 切换到生产网
```bash
# 修改 app/binance/etc/binance.yaml
BaseUrl:
  Http: https://fapi.binance.com
  Ws: wss://fstream.binance.com
```

### 3. 验证配置
```bash
cd app/binance
go run verify_config.go
```

## 获取API密钥

### 测试网API密钥
1. 访问: https://testnet.binance.vision/
2. 使用GitHub账号登录
3. 创建API密钥对
4. 无需KYC验证

### 生产网API密钥
1. 访问: https://www.binance.com/
2. 注册并完成KYC验证
3. 在账户设置中创建API密钥
4. 设置适当的权限和IP白名单

## 安全建议

1. **测试网**:
   - 可以公开API密钥用于测试
   - 建议定期更换密钥

2. **生产网**:
   - 严格保护API密钥
   - 设置IP白名单
   - 只授予必要的权限
   - 定期轮换密钥
   - 使用环境变量存储密钥

## 常见问题

### Q: 测试网的K线数据是真实的吗？
A: 是的，测试网的K线数据与生产网完全一致，都是真实的市场数据。

### Q: 测试网可以进行真实交易吗？
A: 不可以，测试网只能进行模拟交易，使用的是虚拟资金。

### Q: 如何验证当前使用的是哪个环境？
A: 运行 `go run verify_config.go` 可以检查当前配置的环境。
