// Code generated by goctl. DO NOT EDIT.
// Source: algorithm.proto

package server

import (
	"context"

	"zblockchain/app/algorithm/internal/logic"
	"zblockchain/app/algorithm/internal/svc"
	"zblockchain/app/algorithm/pb"
)

type AlgorithmServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedAlgorithmServer
}

func NewAlgorithmServer(svcCtx *svc.ServiceContext) *AlgorithmServer {
	return &AlgorithmServer{
		svcCtx: svcCtx,
	}
}

func (s *AlgorithmServer) MathRes(ctx context.Context, in *pb.MathReq) (*pb.MathResp, error) {
	l := logic.NewMathResLogic(ctx, s.svcCtx)
	return l.MathRes(in)
}
