syntax = "v1"

type UserInfo {
	Nickname string `json:"nickName"`
	PicUrl   string `json:"picUrl"`
	Sex      int64  `json:"sex"`
	Brithday string `json:"brithday"`
}

type Vip {
	Id   int64 `json:"id"`
	Time int64 `json:"time"`
}

type (
	LoginReq {
		Token  string `json:"token"`
		OpenId string `json:"openId"`
	}
	LoginResp {
		Code     int64    `json:"code"`
		Message  string   `json:"message"`
		UserInfo UserInfo `json:"data"`
		VipInfo  []*Vip   `json:"vip"`
	}
)

type (
	PayResultReq {
		OrderId string `json:"orderId"`
		OpenId  string `json:"openId"`
	}
	PayResultRsp {
		Code    int64  `json:"code"`
		VipInfo []*Vip `json:"vip"`
	}
	PayCallbackReq {
		OpenId       string `json:"openId"`
		OrderId      string `json:"orderId"`
		ProductId    string `json:"productId"`
		PayTime      int64  `json:"payTime"`
		Price        int64  `json:"price"`
		ExtrasParams string `json:"extrasParams"`
		Sign         string `json:"sign"`
	}
	PayCallbackRsp {
		Ret int64  `json:"ret"`
		Msg string `json:"msg"`
	}
)

service bmgsdk {
	@doc "login"
	@handler loginVerify
	post /bmgsdk/loginVerify (LoginReq) returns (LoginResp)

	@handler payResult
	post /bmgsdk/payResult (PayResultReq) returns (PayResultRsp)

	@handler payCallback
	post /bmgsdk/payCallback (PayCallbackReq) returns (PayCallbackRsp)
}

