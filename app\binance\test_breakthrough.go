package main

import (
	"fmt"
	"math"
	"time"

	"zblockchain/app/binance/logic"
)

// 创建模拟K线数据 - 模拟高波动突破行情
func createBreakthroughKlines() []*logic.Kline {
	klines := make([]*logic.Kline, 300)
	basePrice := 50000.0
	timestamp := time.Now().Unix() * 1000

	for i := 0; i < 300; i++ {
		// 模拟突破行情：震荡 + 突破 + 回调 + 再突破
		var priceChange float64
		if i < 50 {
			// 初期震荡
			priceChange = math.Sin(float64(i)*0.2) * 200
		} else if i < 80 {
			// 向上突破
			priceChange = 200 + float64(i-50)*30 + math.Sin(float64(i)*0.1)*100
		} else if i < 120 {
			// 回调整理
			priceChange = 1100 - float64(i-80)*15 + math.Sin(float64(i)*0.15)*150
		} else if i < 150 {
			// 向下突破
			priceChange = 500 - float64(i-120)*25 + math.Sin(float64(i)*0.1)*80
		} else if i < 200 {
			// 反弹
			priceChange = -250 + float64(i-150)*20 + math.Sin(float64(i)*0.12)*120
		} else {
			// 高频震荡
			priceChange = 750 + math.Sin(float64(i)*0.3)*300 + math.Cos(float64(i)*0.5)*150
		}

		price := basePrice + priceChange
		
		// 模拟K线数据
		open := price + math.Sin(float64(i)*0.4)*50
		close := price + math.Cos(float64(i)*0.4)*50
		high := math.Max(open, close) + math.Abs(math.Sin(float64(i)*0.6))*100
		low := math.Min(open, close) - math.Abs(math.Cos(float64(i)*0.6))*80
		
		// 突破时成交量放大
		baseVolume := 1000000.0
		volumeMultiplier := 1.0
		if (i >= 50 && i < 80) || (i >= 120 && i < 150) {
			volumeMultiplier = 2.0 + math.Abs(math.Sin(float64(i)*0.1))
		}
		volume := baseVolume * volumeMultiplier

		klines[i] = &logic.Kline{
			Timestamp: timestamp + int64(i)*300000, // 每5分钟
			Open:      open,
			High:      high,
			Low:       low,
			Close:     close,
			Volume:    volume,
		}
	}

	return klines
}

// 创建模拟资金费率数据
func createBreakthroughFundingRates() []*logic.FundingRate {
	rates := make([]*logic.FundingRate, 10) // 5分钟线，300根约25小时，3次资金费率
	timestamp := time.Now().Unix() * 1000

	for i := 0; i < 10; i++ {
		rates[i] = &logic.FundingRate{
			Timestamp: timestamp + int64(i)*8*3600000, // 每8小时
			Rate:      0.0001 + math.Sin(float64(i)*0.2)*0.0001, // 0.01%左右
		}
	}

	return rates
}

func testBreakthroughStrategy() {
	fmt.Println("=== 双向突破+高频止盈策略测试 ===")

	// 创建模拟数据
	klines := createBreakthroughKlines()
	fundingRates := createBreakthroughFundingRates()

	// 设置回测参数
	initialBalance := 500.0
	feeRate := 0.04 / 100
	leverage := 20.0
	stopLossMultiplier := 1.5
	takeProfitMultiplier := 2.0
	maintenanceMarginRate := 0.005

	fmt.Printf("初始资金: %.2f USDT\n", initialBalance)
	fmt.Printf("杠杆倍数: %.1fx\n", leverage)
	fmt.Printf("手续费率: %.3f%%\n", feeRate*100)

	// 测试1: 默认参数
	fmt.Println("\n--- 测试1: 默认参数 ---")
	params := logic.GetDefaultBreakthroughParams()
	bt1 := logic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt1.RunBreakthrough(klines, fundingRates, params, func(klines []*logic.Kline, bt *logic.Backtest, index int, params logic.BreakthroughParams, symbol string, allSymbolPrices map[string]float64) (logic.TradeSignal, float64, string) {
		return logic.BreakthroughStrategy(klines, bt, index, params, symbol, allSymbolPrices)
	})

	fmt.Printf("最终资金: %.2f USDT\n", bt1.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt1.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt1.TradeCount)
	if bt1.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt1.WinCount)/float64(bt1.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt1.MaxDrawdown*100)
	fmt.Printf("当日盈亏: %.2f USDT\n", bt1.BreakthroughDailyPnL)
	fmt.Printf("活跃持仓: %d个\n", len(bt1.GetActiveBreakthroughPositions()))

	// 测试2: 保守型策略
	fmt.Println("\n--- 测试2: 保守型策略 ---")
	conservativeParams := logic.BreakthroughParams{
		EMAFast:           5,
		EMASlow:           20,
		BollingerPeriod:   20,
		BollingerStdDev:   2.0,
		BreakoutThreshold: 0.003, // 0.3%
		TakeProfitMin:     0.008, // 0.8%
		TakeProfitMax:     0.012, // 1.2%
		StopLossMin:       0.004, // 0.4%
		StopLossMax:       0.006, // 0.6%
		PositionSize:      40.0,  // 40U
		LeverageDefault:   15.0,  // 15x
		LeverageMax:       30.0,  // 30x
		MaxPositions:      2,     // 最多2个仓位
		DailyTarget:       80.0,  // 80U日收益目标
		MaxDrawdown:       80.0,  // 80U最大回撤
		VolumeMultiplier:  1.1,   // 成交量1.1倍
		VolumePeriod:      5,     // 5周期成交量
		MomentumPeriod:    8,     // 8周期动量
		InitialBalance:    500.0, // 500U初始资金
	}

	bt2 := logic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt2.RunBreakthrough(klines, fundingRates, conservativeParams, func(klines []*logic.Kline, bt *logic.Backtest, index int, params logic.BreakthroughParams, symbol string, allSymbolPrices map[string]float64) (logic.TradeSignal, float64, string) {
		return logic.BreakthroughStrategy(klines, bt, index, params, symbol, allSymbolPrices)
	})

	fmt.Printf("最终资金: %.2f USDT\n", bt2.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt2.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt2.TradeCount)
	if bt2.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt2.WinCount)/float64(bt2.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt2.MaxDrawdown*100)
	fmt.Printf("当日盈亏: %.2f USDT\n", bt2.BreakthroughDailyPnL)
	fmt.Printf("活跃持仓: %d个\n", len(bt2.GetActiveBreakthroughPositions()))

	// 测试3: 激进型策略
	fmt.Println("\n--- 测试3: 激进型策略 ---")
	aggressiveParams := logic.BreakthroughParams{
		EMAFast:           5,
		EMASlow:           20,
		BollingerPeriod:   20,
		BollingerStdDev:   2.0,
		BreakoutThreshold: 0.007, // 0.7%
		TakeProfitMin:     0.012, // 1.2%
		TakeProfitMax:     0.02,  // 2.0%
		StopLossMin:       0.006, // 0.6%
		StopLossMax:       0.01,  // 1.0%
		PositionSize:      60.0,  // 60U
		LeverageDefault:   25.0,  // 25x
		LeverageMax:       100.0, // 100x
		MaxPositions:      4,     // 最多4个仓位
		DailyTarget:       120.0, // 120U日收益目标
		MaxDrawdown:       120.0, // 120U最大回撤
		VolumeMultiplier:  1.3,   // 成交量1.3倍
		VolumePeriod:      5,     // 5周期成交量
		MomentumPeriod:    12,    // 12周期动量
		InitialBalance:    500.0, // 500U初始资金
	}

	bt3 := logic.NewBacktest(initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	bt3.RunBreakthrough(klines, fundingRates, aggressiveParams, func(klines []*logic.Kline, bt *logic.Backtest, index int, params logic.BreakthroughParams, symbol string, allSymbolPrices map[string]float64) (logic.TradeSignal, float64, string) {
		return logic.BreakthroughStrategy(klines, bt, index, params, symbol, allSymbolPrices)
	})

	fmt.Printf("最终资金: %.2f USDT\n", bt3.Balance)
	fmt.Printf("总收益: %.2f%%\n", (bt3.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("交易次数: %d\n", bt3.TradeCount)
	if bt3.TradeCount > 0 {
		fmt.Printf("胜率: %.2f%%\n", float64(bt3.WinCount)/float64(bt3.TradeCount)*100)
	}
	fmt.Printf("最大回撤: %.2f%%\n", bt3.MaxDrawdown*100)
	fmt.Printf("当日盈亏: %.2f USDT\n", bt3.BreakthroughDailyPnL)
	fmt.Printf("活跃持仓: %d个\n", len(bt3.GetActiveBreakthroughPositions()))

	// 测试4: 参数优化
	fmt.Println("\n--- 测试4: 参数优化 ---")
	bestParams, bestBalance := logic.OptimizeBreakthroughParameters(klines, fundingRates, initialBalance, feeRate, leverage, stopLossMultiplier, takeProfitMultiplier, maintenanceMarginRate)
	
	fmt.Printf("最佳参数:\n")
	fmt.Printf("  EMA: %d/%d, 布林带: %d期/%.1f倍\n", bestParams.EMAFast, bestParams.EMASlow, bestParams.BollingerPeriod, bestParams.BollingerStdDev)
	fmt.Printf("  突破阈值: %.2f%%\n", bestParams.BreakoutThreshold*100)
	fmt.Printf("  止盈: %.2f%%-%.2f%%, 止损: %.2f%%-%.2f%%\n", bestParams.TakeProfitMin*100, bestParams.TakeProfitMax*100, bestParams.StopLossMin*100, bestParams.StopLossMax*100)
	fmt.Printf("  仓位: %.0fU, 杠杆: %.0fx-%.0fx, 最大持仓: %d个\n", bestParams.PositionSize, bestParams.LeverageDefault, bestParams.LeverageMax, bestParams.MaxPositions)
	fmt.Printf("  每日目标: %.0fU, 最大回撤: %.0fU\n", bestParams.DailyTarget, bestParams.MaxDrawdown)
	fmt.Printf("最佳资金: %.2f USDT (收益率: %.2f%%)\n", bestBalance, (bestBalance-initialBalance)/initialBalance*100)

	// 比较结果
	fmt.Println("\n=== 策略对比 ===")
	fmt.Printf("默认策略: %.2f%% 收益\n", (bt1.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("保守策略: %.2f%% 收益\n", (bt2.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("激进策略: %.2f%% 收益\n", (bt3.Balance-initialBalance)/initialBalance*100)
	fmt.Printf("优化策略: %.2f%% 收益\n", (bestBalance-initialBalance)/initialBalance*100)

	// 找出最佳策略
	bestReturn := (bt1.Balance-initialBalance)/initialBalance*100
	bestStrategy := "默认策略"
	
	if (bt2.Balance-initialBalance)/initialBalance*100 > bestReturn {
		bestReturn = (bt2.Balance-initialBalance)/initialBalance*100
		bestStrategy = "保守策略"
	}
	
	if (bt3.Balance-initialBalance)/initialBalance*100 > bestReturn {
		bestReturn = (bt3.Balance-initialBalance)/initialBalance*100
		bestStrategy = "激进策略"
	}
	
	if (bestBalance-initialBalance)/initialBalance*100 > bestReturn {
		bestReturn = (bestBalance-initialBalance)/initialBalance*100
		bestStrategy = "优化策略"
	}

	fmt.Printf("\n🏆 最佳策略: %s (收益率: %.2f%%)\n", bestStrategy, bestReturn)
	
	// 验证策略指标
	fmt.Printf("\n=== 策略验证 ===\n")
	if bestReturn > 0 {
		fmt.Printf("✅ 策略盈利: %.2f%%\n", bestReturn)
	} else {
		fmt.Printf("❌ 策略亏损: %.2f%%\n", bestReturn)
	}
	
	fmt.Printf("支持币种: BTC, ETH, SOL, BNB\n")
	fmt.Printf("推荐时间周期: 1分钟或5分钟线\n")
	fmt.Printf("风险提示: 高杠杆高频交易，需严格风控\n")
}

// func main() {
// 	testBreakthroughStrategy()
// }
