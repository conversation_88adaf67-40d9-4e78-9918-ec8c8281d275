#!/usr/bin/env bash

# 使用方法：
# ./genModel.sh usercenter user
# ./genModel.sh usercenter user_auth
# 再将./genModel下的文件剪切到对应服务的model目录里面，记得改package


#生成的表名
tables=$2
#表生成的genmodel目录
modeldir=./genModel

# 数据库配置
host=**************
port=3308
dbname=zgame_$1
username=root
passwd=123456


echo "开始创建库：$dbname 的表：$2"
goctl model mysql datasource -url="${username}:${passwd}@tcp(${host}:${port})/${dbname}" -table="${tables}"  -dir="${modeldir}" -cache=true --style=goZero
#user
goctl model mysql datasource -url="root:Aa123456@tcp(**************:33069)/zgame" -table="user"  -dir="." -cache=true --style=goZero
#crazysprial_spin_record
goctl model mysql datasource -url="root:Aa123456@tcp(**************:33069)/zgame" -table="crazysprial_spin_record"  -dir="." -cache=true --style=goZero
#lucky777_spin_record
goctl model mysql datasource -url="root:Aa123456@tcp(**************:33069)/zgame" -table="lucky777_spin_record"  -dir="." -cache=true --style=goZero
#jinglefruits_spin_record
goctl model mysql datasource -url="root:Aa123456@tcp(**************:33069)/zgame" -table="jinglefruits_spin_record"  -dir="." -cache=true --style=goZero
#spin_record  L9CTENWNrrjRL9CTENWNrrjR
goctl model mysql datasource -url="root:Aa123456@tcp(**************:33069)/zgame" -table="spin_record"  -dir="." -cache=true --style=goZero


goctl model mysql datasource -url="root:L9CTENWNrrjRL9CTENWNrrjR@tcp(192.168.200.130:3306)/bmgsdk" -table="pay"  -dir="." -cache=false