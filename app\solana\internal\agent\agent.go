package agent

import (
	"sync"
	"time"
	"zblockchain/common/network/core"
	"zblockchain/common/routine"

	"github.com/zeromicro/go-zero/core/logx"
)

type Agent struct {
	mutex     sync.Mutex
	conn      core.Conn
	closeSig  chan bool
	closeFlag bool
	node      int64
}

const (
	MaxChanSize   = 100
	RpcTimeout    = 5 * time.Second
	SlowThreshold = 40 * time.Millisecond
)

var (
	agentMapping      = make(map[int64]*Agent)
	agentMappingMutex sync.Mutex
)

func NewAgent(node int64) core.Agent {
	agt := &Agent{
		node: node,
	}
	return agt
}

func GetAgent(node int64) (*Agent, bool) {
	agentMappingMutex.Lock()
	defer agentMappingMutex.Unlock()
	agent, exists := agentMapping[node]
	return agent, exists
}

// OnConnect is called when a connection is first established.
func (agt *Agent) OnConnect(conn core.Conn) {
	err := routine.Run(true, agt.run)
	if err != nil {
		panic(err)
	}
	agt.conn = conn
	agentMappingMutex.Lock()
	defer agentMappingMutex.Unlock()
	agentMapping[agt.node] = agt
}

// OnMessage is called when a new message is received from the connection.
func (agt *Agent) OnMessage(b []byte) {
	logx.Debugf("%v", string(b))
	// agt.Send(agt.handleMessage(b))
}

// OnClose is called when the connection closed.
func (agt *Agent) OnClose() {
	logx.Debugf("%v is close", agt.node)
}

// func (agt *Agent) handleMessage(b []byte) *gate.ClientResponse {

// 	return nil
// }

// func (agt *Agent) Send(resp *gate.ClientResponse) {
// 	if resp == nil {
// 		return
// 	}

// 	//result, err := proto.Marshal(resp)
// 	//if err != nil {
// 	//	logx.Error("userId : %s, error : %s", agt.userId, err.Error())
// 	//} else {
// 	//	_, err = conn.Write(result)
// 	//	if err != nil {
// 	//		logx.Error("userId : %s, msg write error : %s", agt.userId, err.Error())
// 	//	}
// 	//}
// }

// run
func (agt *Agent) run() {
	err := routine.Run(true, func() {
		defer func() {
			if !agt.closeFlag {
				// 崩溃导致的提前退出
				agt.conn.Close()
			}
		}()

		for {
			select {
			case <-agt.closeSig:
				return
				//case t := <-agt.rpcTask:
				//startTime := time.Now()
				//resp := &gate.ClientResponse{
				//	Id:     t.id,
				//	Method: t.method,
				//}
				//
				//ctx, cancel := context.WithTimeout(contextx.NewContextWithValue(base.UserId, agt.userId), RpcTimeout)
				//methodResp, err := Invoke(GRPC, ctx, t.method, t.content)
				//cancel()
				//
				//if err != nil {
				//	log.Error("userId : %s, req.Method : %s, error : %s", agt.userId, t.method, err.Error())
				//	resp.Code = base.ErrorCodeServiceBusy
				//} else {
				//	resp.Content = methodResp
				//}
				//if agt.conn != nil && !agt.closeFlag {
				//	agt.Send(resp, agt.conn)
				//}
				//
				//// 这里打印 Slow 日志，监控每个 rpc 的调用时间，提醒开发者优化（根据要求，20ms 或 40ms）
				//duration := time.Since(startTime)
				//if duration > SlowThreshold {
				//	log.Warning("Gate slow call, userId : %s, method : %s, content: %v", agt.userId, resp.Method, t.content)
				//}

				//if agt.closeFlag {
				//	return
				//}
			}
		}
	})
	if err != nil {
		agt.conn.Close()
	}
}
