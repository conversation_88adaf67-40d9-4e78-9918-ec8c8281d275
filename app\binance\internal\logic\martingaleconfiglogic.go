package logic

import (
	"context"

	"zblockchain/app/binance/internal/svc"
	"zblockchain/app/binance/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MartingaleConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMartingaleConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MartingaleConfigLogic {
	return &MartingaleConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MartingaleConfigLogic) MartingaleConfig(req *types.MartingaleConfigRequest) (resp *types.MartingaleConfigResponse, err error) {
	// 这里可以实现马丁策略配置的保存逻辑
	// 例如保存到数据库或Redis中

	// 验证参数
	if req.InitialPositionSize <= 0 {
		return &types.MartingaleConfigResponse{
			Success: false,
			Message: "初始仓位大小必须大于0",
		}, nil
	}

	if req.Multiplier <= 1 {
		return &types.MartingaleConfigResponse{
			Success: false,
			Message: "加仓倍数必须大于1",
		}, nil
	}

	if req.MaxLevels <= 0 || req.MaxLevels > 10 {
		return &types.MartingaleConfigResponse{
			Success: false,
			Message: "最大层数必须在1-10之间",
		}, nil
	}

	if req.TakeProfitPercent <= 0 || req.TakeProfitPercent > 1 {
		return &types.MartingaleConfigResponse{
			Success: false,
			Message: "止盈百分比必须在0-100%之间",
		}, nil
	}

	if req.MaxLossPercent <= 0 || req.MaxLossPercent > 1 {
		return &types.MartingaleConfigResponse{
			Success: false,
			Message: "最大亏损百分比必须在0-100%之间",
		}, nil
	}

	// TODO: 这里可以将配置保存到数据库或Redis
	// 目前只是简单返回成功

	return &types.MartingaleConfigResponse{
		Success: true,
		Message: "马丁策略配置保存成功",
	}, nil
}
