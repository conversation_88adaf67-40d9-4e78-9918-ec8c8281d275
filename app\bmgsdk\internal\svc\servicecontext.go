package svc

import (
	"zblockchain/app/bmgsdk/internal/config"
	"zblockchain/app/bmgsdk/model"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type ServiceContext struct {
	Config config.Config

	PayModel model.PayModel
	VipModel model.VipModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	return &ServiceContext{
		Config:   c,
		PayModel: model.NewPayModel(sqlConn),
		VipModel: model.NewVipModel(sqlConn),
	}
}
